<script setup lang="ts"></script>

<template>
  <main>
    <div class="home-container">
      <h1>Cesium 3D 演示项目</h1>
      <div class="demo-links">
        <router-link to="/flowing-pipe" class="demo-link">
          <div class="demo-card">
            <h3>3D流动管道（高级版）</h3>
            <p>展示带有流动特效的3D圆形管道，支持实时调整参数</p>
          </div>
        </router-link>
        <router-link to="/simple-pipe" class="demo-link">
          <div class="demo-card">
            <h3>3D流动管道（简化版）</h3>
            <p>简化版3D管道，带有流动小球动画效果</p>
          </div>
        </router-link>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-container {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

h1 {
  color: #2c3e50;
  margin-bottom: 40px;
  font-size: 2.5rem;
}

.demo-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 40px;
}

.demo-link {
  text-decoration: none;
  color: inherit;
}

.demo-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  cursor: pointer;
}

.demo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.demo-card h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
  color: #00ffff;
}

.demo-card p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
}
</style>
