import {
  Viewer,
  Cartographic,
  Math as CesiumMath,
  Color,
  Cartesian4,
  Cartesian3,
  PostProcessStage,
  Matrix4,
  PostProcessStageComposite
} from "cesium";

export class CircleDiffusion {
  viewer: Viewer;
  lastStageList: any[];
  constructor(viewer: Viewer) {
    this.viewer = viewer;
    this.lastStageList = [];
    // js语法的每行结尾的"分号"可写可不写
  }
  add(position: any, scanColor: any, maxRadius: any, duration: any) {
    this.lastStageList.push(
      this.showCircleScan(position, scanColor, maxRadius, duration)
    );
  }
  clear() {
    this.lastStageList.forEach((ele) => {
      this.clearScanEffects(ele);
    });
    this.lastStageList = [];
  }
  /**
   * 圆扩散
   * @param {*} position  扫描中心 如[117.270739,31.84309,32]
   * @param {*} scanColor 扫描颜色 如"rgba(0,255,0,1)"
   * @param {*} maxRadius 扫描半径，单位米 如1000米
   * @param {*} duration 持续时间，单位毫秒 如4000
   */
  showCircleScan(position: any, scanColor: any, maxRadius: any, duration: any) {
    const cartographicCenter = new Cartographic(
      CesiumMath.toRadians(position[0]),
      CesiumMath.toRadians(position[1]),
      position[2]
    );
    scanColor = Color.fromCssColorString(scanColor);
    const lastStage = this._addCircleScanPostStage(
      cartographicCenter,
      maxRadius,
      scanColor,
      duration
    );
    return lastStage;
  }
  /**
   * 添加扩散效果扫描线
   * @param {*} cartographicCenter  扫描中心
   * @param {*} maxRadius 扫描半径
   * @param {*} scanColor  扫描颜色
   * @param {*} duration  持续时间
   */
  _addCircleScanPostStage(cartographicCenter: any, maxRadius: any, scanColor: any, duration: number) {
    const _Cartesian3Center =
      Cartographic.toCartesian(cartographicCenter);
    const _Cartesian4Center = new Cartesian4(
      _Cartesian3Center.x,
      _Cartesian3Center.y,
      _Cartesian3Center.z,
      1
    );
    
    // 修改高度偏移，确保法线计算正确
    const _CartograhpicCenter1 = new Cartographic(
      cartographicCenter.longitude,
      cartographicCenter.latitude,
      cartographicCenter.height + 1000  // 增加高度差以确保法线更明显
    );
    
    const _Cartesian3Center1 =
      Cartographic.toCartesian(_CartograhpicCenter1);
    const _Cartesian4Center1 = new Cartesian4(
      _Cartesian3Center1.x,
      _Cartesian3Center1.y,
      _Cartesian3Center1.z,
      1
    );
    const _time = new Date().getTime();
    const _scratchCartesian4Center = new Cartesian4();
    const _scratchCartesian4Center1 = new Cartesian4();
    const _scratchCartesian3Normal = new Cartesian3();
    const _this = this;
    const ScanPostStage = new PostProcessStage({
      fragmentShader: _this._getScanSegmentShader(),
      uniforms: {
        u_scanCenterEC: function () {
          const temp = Matrix4.multiplyByVector(
            // _this.viewer.camera._viewMatrix,
            _this.viewer.camera.viewMatrix,
            _Cartesian4Center,
            _scratchCartesian4Center
          );
          return temp;
        },
        u_scanPlaneNormalEC: function () {
          const temp = Matrix4.multiplyByVector(
            _this.viewer.camera.viewMatrix,
            _Cartesian4Center,
            _scratchCartesian4Center
          );
          const temp1 = Matrix4.multiplyByVector(
            _this.viewer.camera.viewMatrix,
            _Cartesian4Center1,
            _scratchCartesian4Center1
          );
          _scratchCartesian3Normal.x = temp1.x - temp.x;
          _scratchCartesian3Normal.y = temp1.y - temp.y;
          _scratchCartesian3Normal.z = temp1.z - temp.z;
          Cartesian3.normalize(
            _scratchCartesian3Normal,
            _scratchCartesian3Normal
          );
          return _scratchCartesian3Normal;
        },
        // 修改半径计算方式，使其更平滑
        u_radius: function () {
          // 使用正弦函数使扩散效果更加平滑
          const elapsedTime = (new Date().getTime() - _time) % duration;
          const normalizedTime = elapsedTime / duration;
          return maxRadius * normalizedTime;
        },
        u_scanColor: scanColor,
      },
    });
    
    // 确保后处理阶段被添加到场景中
    this.viewer.scene.postProcessStages.add(ScanPostStage);
    return ScanPostStage;
  }
  
  /**
   * 扩散效果Shader
   */
  _getScanSegmentShader() {
    const inpram = 18;  // 控制边缘锐利度
    const scanSegmentShader =
      ` uniform sampler2D colorTexture;
        uniform sampler2D depthTexture;
        in vec2 v_textureCoordinates;
        uniform vec4 u_scanCenterEC;
        uniform vec3 u_scanPlaneNormalEC;
        uniform float u_radius;
        uniform vec4 u_scanColor;
        out vec4 fragColor;
        
        vec4 toEye(in vec2 uv, in float depth){
          vec2 xy = vec2((uv.x * 2.0 - 1.0),(uv.y * 2.0 - 1.0));
          vec4 posInCamera = czm_inverseProjection * vec4(xy, depth, 1.0);
          posInCamera = posInCamera / posInCamera.w;
          return posInCamera;
        }
        
        vec3 pointProjectOnPlane(in vec3 planeNormal, in vec3 planeOrigin, in vec3 point){
            vec3 v01 = point - planeOrigin;
            float d = dot(planeNormal, v01);
            return (point - planeNormal * d);
        }
        
        float getDepth(in vec4 depth){
            float z_window = czm_unpackDepth(depth);
            z_window = czm_reverseLogDepth(z_window);
            float n_range = czm_depthRange.near;
            float f_range = czm_depthRange.far;
            return (2.0 * z_window - n_range - f_range) / (f_range - n_range);
        }
        
        void main(){
          fragColor = texture(colorTexture, v_textureCoordinates);
          float depth = getDepth(texture(depthTexture, v_textureCoordinates));
          
          // 处理深度为1.0的情况（天空或远处）
          if (depth >= 0.999) {
            return;
          }
          
          vec4 viewPos = toEye(v_textureCoordinates, depth);
          vec3 prjOnPlane = pointProjectOnPlane(u_scanPlaneNormalEC.xyz, u_scanCenterEC.xyz, viewPos.xyz);
          float dis = length(prjOnPlane.xyz - u_scanCenterEC.xyz);
          
          // 环形效果参数
          float ringWidth = u_radius * 0.08; // 环宽度
          float glowRange = u_radius * 0.15; // 光晕范围
          
          // 主环
          if(abs(dis - u_radius) < ringWidth) {
            // 计算环内位置的强度
            float f = 1.0 - abs(dis - u_radius) / ringWidth;
            f = pow(f, 1.5); // 调整边缘平滑度
            
            // 应用颜色
            fragColor = mix(fragColor, u_scanColor, f * 0.8);
          }
          // 外部光晕
          else if(dis < u_radius + glowRange && dis > u_radius - ringWidth) {
            float f = 1.0 - (dis - (u_radius - ringWidth)) / glowRange;
            f = pow(f, 2.0); // 使光晕更加柔和
            
            // 应用较淡的光晕效果
            vec4 glowColor = u_scanColor;
            glowColor.a = u_scanColor.a * 0.5; // 降低光晕透明度
            
            fragColor = mix(fragColor, glowColor, f * 0.3);
          }
        }
      `;
    return scanSegmentShader;
  }
  /**
   * 清除特效对象
   * @param {*} lastStage 特效对象
   */
  clearScanEffects(lastStage: PostProcessStage | PostProcessStageComposite) {
    this.viewer.scene.postProcessStages.remove(lastStage);
  }
}
