<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-05
 * @Description: 初始化cesium, 并添加了自带的3d建筑图层
 * @FilePath: \demo2\src\components\mapLevel.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import 'cesium/Build/Cesium/Widgets/widgets.css'
import {
  Cartesian3,
  createOsmBuildingsAsync,
  Ion,
  Terrain,
  Viewer,
  ScreenSpaceEventHandler,
  ScreenSpaceEventType,
  defined,
  Cartographic,
  Math as CesiumMath,
  Cesium3DTileset,
  Color,
  NearFarScalar,
  Cartesian2,
} from 'cesium'
import { onMounted, onBeforeUnmount } from 'vue'
import {
  load3DTile,
  addPipe,
  printPosition,
  addParticleFire,
  addPlaneFlowRoad,
  addGlowingPolyline,
  addGlowingPolylineEntity,
  createGlowingPolyline,
  addGlowingPolygon,
  addRotatingGroundImage,
} from './mapLevel'
import { useFence } from './demo0Fence'
import { CircleDiffusion } from './demo0Diffuse'

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg'

let viewer: Viewer
let tileset: Cesium3DTileset | undefined

onMounted(() => {
  initCesiumMap()
})

onBeforeUnmount(() => {
  if (viewer) viewer.destroy()
  if (tileset) tileset.destroy()
})

/**
 * @description: 实例化cesium
 */
async function initCesiumMap() {
  // 初始化地图并加载地形图
  viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
    shouldAnimate: true,

    timeline: false, // 直接关闭时间轴控件[2,5](@ref)
    animation: false, // 若需同时隐藏左下角动画控件
    // 隐藏右上角工具栏组件
    fullscreenButton: false, // 全屏按钮
    vrButton: false, // VR模式切换
    sceneModePicker: false, // 2D/3D/哥伦布视图切换
    baseLayerPicker: false, // 底图选择器（若需保留影像切换功能可不设置）
    geocoder: false, // 搜索框（位于左上角，但常与工具栏联动）
    homeButton: false, // 返回主视图按钮
    navigationHelpButton: false, // 帮助信息按钮
  })
  ;(viewer.cesiumWidget.creditContainer as HTMLDivElement).style.display = 'none' // 隐藏版权信息

  viewer.scene.globe.depthTestAgainstTerrain = true // 启用地形深度检测
  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false

  viewer.scene.globe.translucency.enabled = true // 启用地球半透模式
  viewer.scene.globe.translucency.frontFaceAlpha = 0.9 // 正面透明度

  // 缩到面前时透明，远了就不透明
  // viewer.scene.globe.translucency.frontFaceAlphaByDistance = new NearFarScalar(
  //       400.0,
  //       0.0,
  //       800.0,
  //       1.0
  //   );

  viewer.scene.globe.enableLighting = false // 关闭全局光照，降低计算量
  // 关闭抗锯齿（FXAA）或阴影效果（如果启用）。
  viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.shadows = false
  viewer.scene.debugShowFramesPerSecond = true // 显示帧率

  // 1米内的剔除；防止模型闪烁
  viewer.camera.frustum.near = 1

  // 直接飞到邯郸
  // viewer.camera.flyTo({
  //   destination: Cartesian3.fromDegrees(114.497398, 36.598983, 400),
  //   orientation: {
  //     heading: CesiumMath.toRadians(0.0),
  //     pitch: CesiumMath.toRadians(-15.0),
  //   }
  // });

  // 加载3D模型
  const tileset = await load3DTile(viewer)

  // 根据地图等级控制渲染
  let pipes: any = null
  let pipe1: any = null
  let pipe2: any = null
  let glowingLine: any = null
  let flowingLine: any = null

  pipes = addPipe(viewer)

  const updateRenderingByZoom = () => {
    const height = viewer.camera.positionCartographic.height
    const zoomLevel = Math.log2(40075016.686 / height) // 计算缩放等级
    console.log(
      '%c [ zoomLevel ]-130',
      'font-size:13px; background:#3b3ce6; color:#7f80ff;',
      zoomLevel,
    )

    // 1. 定义圆形截面生成函数（核心参数：半径）
    const computeCircle = (radius: number) => {
      const positions = []
      for (let i = 0; i < 360; i++) {
        const radians = CesiumMath.toRadians(i)
        positions.push(
          new Cartesian2(
            radius * Math.cos(radians), // X轴坐标
            radius * Math.sin(radians), // Y轴坐标
          ),
        )
      }
      return positions // 返回二维点数组描述圆形截面[1,2](@ref)
    }

    if (zoomLevel > 15) {
      // 直接更新实体的shape属性
      pipes.polylineVolume.shape = computeCircle(2)

      // 显示管道，隐藏折线
      // if (!pipes) {
      //   pipes = addPipe(viewer)
      // }
      // if (pipe1) {
      //   viewer.entities.remove(pipe1)
      //   pipe1 = null
      // }
      // if (pipe2) {
      //   viewer.entities.remove(pipe2)
      //   pipe2 = null
      // }

      // if (glowingLine) {
      //   viewer.entities.remove(glowingLine)
      //   glowingLine = null
      // }
      // if (flowingLine) {
      //   viewer.entities.remove(flowingLine)
      //   flowingLine = null
      // }
    } else if (zoomLevel > 13) {
      pipes.polylineVolume.shape = computeCircle(10)

      // // 显示管道，隐藏折线
      // if (pipes) {
      //   viewer.entities.remove(pipes)
      //   pipes = null
      // }
      // if (!pipe1) {
      //   pipe1 = addPipe(viewer, 10)
      // }
      // if (pipe2) {
      //   viewer.entities.remove(pipe2)
      //   pipe2 = null
      // }

      // if (glowingLine) {
      //   viewer.entities.remove(glowingLine)
      //   glowingLine = null
      // }
      // if (flowingLine) {
      //   viewer.entities.remove(flowingLine)
      //   flowingLine = null
      // }
    } else {
      pipes.polylineVolume.shape = computeCircle(40)

      // 显示折线，隐藏管道
      // if (pipes) {
      //   viewer.entities.remove(pipes)
      //   pipes = null
      // }
      // if (pipe1) {
      //   viewer.entities.remove(pipe1)
      //   pipe1 = null
      // }
      // if (!pipe2) {
      //   pipe2 = addPipe(viewer, 40)
      // }

      // if (!glowingLine) {
      //   glowingLine = addGlowingPolylineEntity(viewer, [
      //     [120.727823, 31.266826, 10],
      //     [120.737823, 31.266826, 10],
      //     [120.737823, 31.276826, 10],
      //     [120.727823, 31.276826, 10],
      //   ])
      // }
      // if (!flowingLine) {
      //   flowingLine = createGlowingPolyline(viewer, [
      //     [120.727823, 31.266826, 11],
      //     [120.737823, 31.266826, 11],
      //     [120.737823, 31.276826, 11],
      //     [120.727823, 31.276826, 11],
      //   ])
      // }
    }
  }

  // 初始渲染
  updateRenderingByZoom()

  // 监听相机变化
  viewer.camera.changed.addEventListener(updateRenderingByZoom)

  // 添加管道
  // const pipes = addPipe(viewer)

  // TODO 添加流动的平面管道
  // await addPlaneFlowRoad(viewer);

  // // 添加火焰
  // await addParticleFire(viewer);

  // // 电子围墙
  // useFence(viewer)

  // // 圆扩散
  // const circleDiffusion = new CircleDiffusion(viewer);
  // const start = () => {
  //   circleDiffusion.add([120.728823, 31.255826, 10], "#00ffff", 2000, 5000);
  // };
  // start();

  // 示例：创建一条发光折线
  // const glowingLine = addGlowingPolyline(viewer, [
  //   120.728823, 31.255826, 10,  // 第一个点
  //   120.738823, 31.255826, 10,  // 第二个点
  //   120.738823, 31.265826, 10,  // 第三个点
  //   120.728823, 31.265826, 10   // 第四个点
  // ], {
  //   width: 85,                                  // 自定义线宽
  //   glowPower: 0.1,                             // 自定义发光强度
  //   color: Color.fromCssColorString('#00ffff'), // 自定义线条颜色
  //   glowColor: Color.fromCssColorString('#00ffff') // 自定义发光颜色
  // });

  // // TODO 发光折线2
  // addGlowingPolylineEntity(viewer, [
  //   // [120.729823, 31.256826, 10],  // 第一个点
  //   // [120.739823, 31.256826, 10],  // 第二个点
  //   // [120.739823, 31.266826, 10],  // 第三个点
  //   // [120.729823, 31.266826, 10]   // 第四个点
  //   [120.727823, 31.266826, 10], // 第一个点
  //   [120.737823, 31.266826, 10], // 第二个点
  //   [120.737823, 31.276826, 10], // 第三个点
  //   [120.727823, 31.276826, 10], // 第四个点
  // ])

  // // TODO 创建蓝色流光折线
  // createGlowingPolyline(
  //   viewer,
  //   [
  //     [120.727823, 31.266826, 11], // 第一个点
  //     [120.737823, 31.266826, 11], // 第二个点
  //     [120.737823, 31.276826, 11], // 第三个点
  //     [120.727823, 31.276826, 11], // 第四个点
  //   ], // 流动速度
  // )

  //  TODO 基础发光多边形
  // const glowingArea = addGlowingPolygon(viewer, [
  //   120.728823, 31.255826,
  //   120.738823, 31.255826,
  //   120.738823, 31.265826,
  //   120.728823, 31.265826
  // ], {
  //   // polygonColor: Color.fromCssColorString('#3388ff').withAlpha(0.2),
  //   // glowColor: Color.fromCssColorString('#00ffff'),
  //   // glowWidth: 12
  // });

  // TODO 地面旋转图片
  // const rotatingImage = addRotatingGroundImage(viewer);

  // // 设置相机位置并飞行到tileset
  try {
    // 自动定位到模型
    // await viewer.zoomTo(tileset!)
    // console.log('相机已定位到模型')

    viewer.camera.flyTo({
      destination: Cartesian3.fromDegrees(120.727823, 31.266826, 5000),
    })

    // // 保存当前视图作为home视图
    // viewer.homeButton.viewModel.command.beforeExecute.addEventListener(function (e) {
    //   e.cancel = true;
    //   viewer.zoomTo(tileset!);
    // });
  } catch (error) {
    console.warn('自动定位到模型失败，使用默认位置:', error)
    // 使用手动设置的相机位置作为备选
    viewer.camera.flyTo({
      destination: Cartesian3.fromDegrees(116.4074, 39.9042, 5000), // 默认位置（北京）
    })
  }

  // 输出点击位置的坐标
  printPosition(viewer)
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
