import {
  Viewer,
  ScreenSpaceEventHandler,
  defined,
  Cartographic,
  ScreenSpaceEventType,
  Math as CesiumMath,
  Cesium3DTileset,
  Cartesian3,
  Matrix4,
  Cartesian2,
  Color,
  CornerType,
  Cesium3DTileStyle,
  ParticleSystem,
  ConeEmitter,
  ParticleBurst,
  CircleEmitter,
  HeadingPitchRoll,
  Quaternion,
  TranslationRotationScale,
  Transforms,
  Ellipsoid,
  PolylineGeometry,
  PolylineMaterialAppearance,
  GeometryInstance,
  Material,
  Primitive,
  MaterialProperty,
  Property,
  Event,
  JulianDate,
  PolylineGlowMaterialProperty,
  CustomShader,
  LightingModel,
  ImageMaterialProperty,
  HeightReference,
  CallbackProperty,
  ClassificationType,
  Plane,
  VerticalOrigin,
  CallbackPositionProperty,
  PolylineVolumeGeometry,
  ColorGeometryInstanceAttribute,
  PerInstanceColorAppearance
  // 移除 createPropertyDescriptor 导入
} from 'cesium'

/**
 * @description: 输出点击位置的坐标
 */
export function printPosition(viewer: Viewer) {
  var handler = new ScreenSpaceEventHandler(viewer.canvas)
  handler.setInputAction(function (event: any) {
    // 获取点击位置的笛卡尔坐标
    var pickedPosition = viewer.scene.pickPosition(event.position)

    if (defined(pickedPosition)) {
      // 将笛卡尔坐标转换为经纬度坐标
      var cartographic = Cartographic.fromCartesian(pickedPosition)
      var longitude = CesiumMath.toDegrees(cartographic.longitude) // 经度
      var latitude = CesiumMath.toDegrees(cartographic.latitude) // 纬度
      var height = cartographic.height // 高度，单位为米

      // 也可以输出原始笛卡尔坐标
      console.log('笛卡尔坐标：', pickedPosition)
      console.log(
        '%c [ cartographic ]-60',
        'font-size:13px; background:#0738f5; color:#4b7cff;',
        cartographic,
      )

      // 输出经纬度坐标
      console.log('经纬高：', longitude.toFixed(6), latitude.toFixed(6), height.toFixed(2))
    }
  }, ScreenSpaceEventType.LEFT_CLICK)
}

/**
 * @description: 开始加载3D模型
 */
export async function load3DTile(viewer: Viewer) {
  try {
    console.log('开始加载3D模型...')
    // 加载.b3dm文件的示例 /serverApi/static/3dtiles/gyz3dtiles/tileset.json    /static/tengfei/tileset.json
    const tileset = await Cesium3DTileset.fromUrl('/static/tengfei/tileset.json', {
      // skipLevelOfDetail: true,
      // baseScreenSpaceError: 1024,
      // skipScreenSpaceErrorFactor: 16,
      // skipLevels: 1,
      // immediatelyLoadDesiredLevelOfDetail: false,
      // loadSiblings: false,
      // cullWithChildrenBounds: true,

      // dynamicScreenSpaceError: true,
      // dynamicScreenSpaceErrorDensity: 2.0e-4,
      // dynamicScreenSpaceErrorFactor: 24.0, // 较大的值会导致加载较低分辨率的图块，从而略微提高运行时性能 降低视觉质量。该值必须为非负数
      // dynamicScreenSpaceErrorHeightFalloff: 0.25,

      // // 优化选项。移动时剔除请求中使用的乘数。较大的剔除较为激进，较小的剔除较不激进。默认60
      // cullRequestsWhileMovingMultiplier: 60,
      // // 以控制决定延迟哪些图块的圆锥体大小。 此圆锥体内的切片将立即加载。圆锥体外的图块可能会根据它们在圆锥体外的距离而延迟加载。
      // // 默认值为 0.3, 取值在 0~ 1 之间。值越小，性能越好，模糊区域越多
      // foveatedConeSize: 0.3,
      // maximumCacheOverflowBytes: 536870912, // 将用于缓存切片的最大额外 GPU 内存量
      // maximumScreenSpaceError: 24, // 用于驱动细节层次优化的最大屏幕空间误差。此值有助于确定何时显示 优化到其后代. 值越高，性能越好，但视觉质量越低
      // preloadWhenHidden: false, // 预加载图块。加载瓦片，就好像瓦片集可见一样，但不渲染它们

      // // 开发调试用
      // debugColorizeTiles: false, // 为每个平铺分配随机颜色。这对于可视化 哪些特征属于哪些切片，尤其是使用加法细化 Where 特征 From Parent 切片可以与 Child Tiles 中的要素交错。
      // debugShowMemoryUsage: false, // 绘制标签以指示每个图块的几何图形和纹理内存使用情况。
    })

    // 把模型弄为半透明
    // tileset.style = new Cesium3DTileStyle({
    //   color: 'color("rgba(255, 255, 255, 0.9)")'
    // });

    console.log('3D模型加载成功，添加到场景...')
    // 添加tileset到场景
    viewer.scene.primitives.add(tileset)

    // 等待模型加载完成
    // await tileset.readyPromise;
    console.log('模型准备完成，设置相机位置...')

    // 修复模型位置偏移问题
    // 1. 禁用模型与地形的交互
    tileset.show = true

    // 2. 设置正确的高度模式
    // // tileset.heightReference = undefined;
    // // 获取模型的中心位置
    // const boundingSphere = tileset.boundingSphere;
    // const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(boundingSphere.center);
    // // 设置新的高度
    // const newHeight = 36; // 设置新的绝对高度，单位为米
    // cartographic.height = newHeight;
    // // 计算新的中心位置
    // const newCenter = viewer.scene.globe.ellipsoid.cartographicToCartesian(cartographic);
    // // 计算偏移量
    // const offset = Cartesian3.subtract(newCenter, boundingSphere.center, new Cartesian3());
    // // 应用偏移量
    // const translation = Matrix4.fromTranslation(offset);
    // tileset.modelMatrix = translation;

    // 调整模型高度
    const heightOffset = 86.0
    const boundingSphere = tileset.boundingSphere
    const cartographic = Cartographic.fromCartesian(boundingSphere.center)
    const surface = Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0.0)
    const offset = Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      heightOffset,
    )
    const translation = Cartesian3.subtract(offset, surface, new Cartesian3())
    tileset.modelMatrix = Matrix4.fromTranslation(translation)

    return tileset
  } catch (error: any) {
    console.error('加载3D Tiles失败:', error)
    alert('3D模型加载失败: ' + error.message)
  }
}

// 使用Primitive创建PolylineVolume
function createPolylineVolumePrimitive(positions: any, shape: any, material: any) {
  const polylineVolumeGeometry = new PolylineVolumeGeometry({
    polylinePositions: positions,
    shapePositions: shape,
    cornerType: CornerType.ROUNDED
  });

  const geometryInstance = new GeometryInstance({
    geometry: polylineVolumeGeometry,
    attributes: {
      color: ColorGeometryInstanceAttribute.fromColor(material.color)
    }
  });

  return new Primitive({
    geometryInstances: geometryInstance,
    appearance: new PerInstanceColorAppearance({
      translucent: material.translucent
    }),
    asynchronous: false // 同步加载，避免异步导致的延迟
  });
}

export function addPipeDemo(viewer: Viewer) {
  const arr1 = [
    114.19956725,
    36.46087588,
    200,
    114.19849636,
    36.46089007,
    200,
    114.19653484,
    36.46058628,
    200,
    114.1951003,
    36.4600258,
    200,
    114.19491774,
    36.45991312,
    200,
    114.19364567,
    36.45874953,
    200
  ];
  const arr2 = [
    114.19956725,
    36.46087589,
    200,
    114.20093152,
    36.46061664,
    200,
    114.20176556,
    36.46032544,
    200,
    114.20188671,
    36.46027943,
    200,
    114.20292704,
    36.45988427,
    200,
    114.20453039,
    36.45960254,
    200,
    114.20471727,
    36.45960009,
    200,
    114.20482783,
    36.45960152,
    200,
    114.2051903,
    36.4596062,
    200,
    114.20545274,
    36.4596096,
    200,
    114.20579749,
    36.45944624,
    200,
    114.20738275,
    36.45946458,
    200,
    114.20751661,
    36.45946613,
    200,
    114.20765047,
    36.45946768,
    200,
    114.20764855,
    36.45957581,
    200,
    114.20763809,
    36.46016621,
    200,
    114.20763618,
    36.46027434,
    200,
    114.20776168,
    36.46031197,
    200,
    114.20796789,
    36.46037379,
    200,
    114.21070989,
    36.46061184,
    200,
    114.21136891,
    36.46053581,
    200,
    114.21273296,
    36.46034919,
    200,
    114.21290354,
    36.46032585,
    200,
    114.21303553,
    36.46030779,
    200,
    114.21316752,
    36.46028973,
    200,
    114.21329952,
    36.46027167,
    200,
    114.21343151,
    36.46025361,
    200,
    114.21339245,
    36.46006736,
    200,
    114.21352445,
    36.4600493,
    200,
    114.21354374,
    36.46004666,
    200,
    114.21365644,
    36.46003124,
    200,
    114.21380553,
    36.46001084,
    200,
    114.21391388,
    36.45999601,
    200,
    114.21404587,
    36.45997795,
    200,
    114.21417787,
    36.45995989,
    200,
    114.21420525,
    36.46009048,
    200,
    114.21421696,
    36.46014632,
    200,
    114.21434904,
    36.46012867,
    200,
    114.21448112,
    36.46011102,
    200,
    114.21455451,
    36.46010121,
    200,
    114.2146132,
    36.46009337,
    200,
    114.21474527,
    36.46007572,
    200,
    114.21487735,
    36.46005807,
    200,
    114.21669785,
    36.45981478,
    200,
    114.21760993,
    36.45969288,
    200,
    114.217742,
    36.45967523,
    200,
    114.21797278,
    36.45964438,
    200,
    114.21871275,
    36.45954548,
    200,
    114.21880234,
    36.45998284,
    200,
    114.21990373,
    36.45983561,
    200,
    114.22015769,
    36.46069707,
    200,
    114.22062287,
    36.46227496,
    200,
    114.22187362,
    36.46448257,
    200,
    114.22193354,
    36.46457928,
    200,
    114.22199759,
    36.46467425,
    200,
    114.22212333,
    36.46484799,
    200,
    114.223361,
    36.4665582,
    200,
    114.22342276,
    36.46664354,
    200,
    114.22349409,
    36.46673519,
    200,
    114.22356934,
    36.46682478,
    200,
    114.22364819,
    36.46691194,
    200,
    114.22485516,
    36.46828449,
    200,
    114.22493401,
    36.46837417,
    200,
    114.22506166,
    36.46851932,
    200,
    114.22513911,
    36.46860753,
    200,
    114.22521657,
    36.46869574,
    200,
    114.2253644,
    36.46861106,
    200,
    114.22552021,
    36.46852181,
    200,
    114.22578204,
    36.46882001,
    200,
    114.22623604,
    36.46935686,
    200,
    114.2265991,
    36.46998924,
    200,
    114.22665543,
    36.47008734,
    200,
    114.22671176,
    36.47018545,
    200,
    114.22691012,
    36.47053095,
    200,
    114.22696911,
    36.47062802,
    200,
    114.2270281,
    36.4707251,
    200,
    114.22708709,
    36.47082218,
    200,
    114.22714608,
    36.47091926,
    200,
    114.22726406,
    36.47111341,
    200,
    114.22738205,
    36.47130757,
    200,
    114.22745087,
    36.47142082,
    200,
    114.22755902,
    36.4715988,
    200,
    114.227677,
    36.47179295,
    200,
    114.22785398,
    36.47208419,
    200,
    114.2284057,
    36.47299208,
    200,
    114.22855318,
    36.47323477,
    200,
    114.22858268,
    36.47328331,
    200,
    114.22864167,
    36.47338038,
    200,
    114.22870067,
    36.47347746,
    200,
    114.22875966,
    36.47357454,
    200,
    114.22881442,
    36.47367322,
    200,
    114.22886489,
    36.47377338,
    200,
    114.228911,
    36.47387491,
    200,
    114.22920375,
    36.47447913,
    200,
    114.22925255,
    36.47457983,
    200,
    114.22930134,
    36.47468053,
    200,
    114.22983508,
    36.47578208,
    200,
    114.22988387,
    36.47588278,
    200,
    114.22993267,
    36.47598349,
    200,
    114.22998146,
    36.47608419,
    200,
    114.22999241,
    36.47619196,
    200,
    114.23000337,
    36.47629974,
    200,
    114.23001432,
    36.47640752,
    200,
    114.23012019,
    36.47744933,
    200,
    114.23018086,
    36.4779025,
    200,
    114.23012763,
    36.4779636,
    200,
    114.22999923,
    36.47811098,
    200,
    114.22976806,
    36.4783763,
    200,
    114.22969101,
    36.47846474,
    200,
    114.22955616,
    36.47861951,
    200,
    114.22949837,
    36.47868584,
    200,
    114.22960788,
    36.47874807,
    200,
    114.22971735,
    36.47881028,
    200,
    114.2299593,
    36.47894776,
    200,
    114.22994337,
    36.47941221,
    200,
    114.22994283,
    36.47942859,
    200,
    114.22992921,
    36.47984462,
    200,
    114.22991506,
    36.48027702,
    200,
    114.2299009,
    36.48070942,
    200,
    114.22988674,
    36.48114183,
    200,
    114.2298832,
    36.48124993,
    200,
    114.22987966,
    36.48135803,
    200,
    114.22987612,
    36.48146613,
    200,
    114.22987258,
    36.48157423,
    200,
    114.22970997,
    36.48301771,
    200,
    114.2293484,
    36.48299115,
    200,
    114.22925742,
    36.4837987,
    200,
    114.22971451,
    36.48383228,
    200,
    114.22962239,
    36.48464999,
    200,
    114.22966606,
    36.48623083,
    200,
    114.23025835,
    36.487805,
    200,
    114.2307784,
    36.48874258,
    200,
    114.2311296,
    36.48861556,
    200,
    114.23198219,
    36.49016562,
    200,
    114.23171398,
    36.49026181,
    200,
    114.23181763,
    36.49045028,
    200,
    114.23227332,
    36.49127883,
    200,
    114.23243638,
    36.49157532,
    200,
    114.23249074,
    36.49167415,
    200,
    114.2325451,
    36.49177298,
    200,
    114.23259945,
    36.49187182,
    200,
    114.23270817,
    36.49206948,
    200,
    114.23287124,
    36.49236597,
    200,
    114.2330657,
    36.49271953,
    200,
    114.2339214,
    36.49427702,
    200,
    114.23397569,
    36.49437588,
    200,
    114.23402998,
    36.49447473,
    200,
    114.23408427,
    36.49457359,
    200,
    114.23413856,
    36.49467244,
    200,
    114.23419285,
    36.4947713,
    200,
    114.23424714,
    36.49487016,
    200,
    114.23430144,
    36.49496901,
    200,
    114.23435573,
    36.49506787,
    200,
    114.23441002,
    36.49516672,
    200,
    114.23446431,
    36.49526558,
    200,
    114.2345186,
    36.49536443,
    200,
    114.23457289,
    36.49546329,
    200,
    114.23462718,
    36.49556214,
    200,
    114.23468148,
    36.495661,
    200,
    114.23473577,
    36.49575985,
    200,
    114.23479006,
    36.49585871,
    200,
    114.23484435,
    36.49595756,
    200,
    114.23489865,
    36.49605642,
    200,
    114.23495294,
    36.49615528,
    200,
    114.23500723,
    36.49625413,
    200,
    114.23506152,
    36.49635299,
    200,
    114.23511582,
    36.49645184,
    200,
    114.23517011,
    36.4965507,
    200,
    114.2352244,
    36.49664955,
    200,
    114.2352787,
    36.49674841,
    200,
    114.23533299,
    36.49684726,
    200,
    114.23538728,
    36.49694612,
    200,
    114.23544158,
    36.49704497,
    200,
    114.23549587,
    36.49714383,
    200,
    114.23555017,
    36.49724268,
    200,
    114.23560446,
    36.49734154,
    200,
    114.23565875,
    36.49744039,
    200,
    114.23571305,
    36.49753925,
    200,
    114.23576734,
    36.4976381,
    200,
    114.23582164,
    36.49773695,
    200,
    114.23587593,
    36.49783581,
    200,
    114.23593023,
    36.49793466,
    200,
    114.23713397,
    36.49750368,
    200,
    114.23736272,
    36.49792182,
    200,
    114.23952522,
    36.49714752,
    200,
    114.24014214,
    36.49693694,
    200,
    114.24076379,
    36.496735,
    200,
    114.24138933,
    36.49654196,
    200,
    114.24203184,
    36.49635398,
    200,
    114.24287211,
    36.49610814,
    200,
    114.24329106,
    36.49598557,
    200,
    114.24407908,
    36.49575555,
    200,
    114.24427247,
    36.49569842,
    200,
    114.24457912,
    36.49561848,
    200,
    114.24481346,
    36.49620017,
    200,
    114.2450862,
    36.49733402,
    200,
    114.24512477,
    36.49749437,
    200,
    114.24644242,
    36.49728774,
    200,
    114.24797967,
    36.50367738,
    200,
    114.25543328,
    36.50250827,
    200,
    114.26293925,
    36.51074567,
    200,
    114.26305077,
    36.51071987,
    200,
    114.26317955,
    36.51069008,
    200,
    114.26330832,
    36.51066029,
    200,
    114.2634371,
    36.5106305,
    200,
    114.26356587,
    36.51060071,
    200,
    114.26369465,
    36.51057092,
    200,
    114.26382342,
    36.51054113,
    200,
    114.2639522,
    36.51051134,
    200,
    114.26408097,
    36.51048154,
    200,
    114.26420975,
    36.51045175,
    200,
    114.26433852,
    36.51042196,
    200,
    114.2644673,
    36.51039217,
    200,
    114.26459607,
    36.51036238,
    200,
    114.26472485,
    36.51033259,
    200,
    114.26485362,
    36.51030279,
    200,
    114.2649824,
    36.510273,
    200,
    114.26511117,
    36.51024321,
    200,
    114.26523995,
    36.51021342,
    200,
    114.26536872,
    36.51018362,
    200,
    114.26549749,
    36.51015383,
    200,
    114.26562627,
    36.51012404,
    200,
    114.26575504,
    36.51009424,
    200,
    114.26588382,
    36.51006445,
    200,
    114.26601259,
    36.51003466,
    200,
    114.26614136,
    36.51000486,
    200,
    114.26639891,
    36.50994528,
    200,
    114.26652768,
    36.50991548,
    200,
    114.26665646,
    36.50988569,
    200,
    114.26678523,
    36.50985589,
    200,
    114.26710716,
    36.50978141,
    200,
    114.26742909,
    36.50970692,
    200,
    114.26807296,
    36.50955794,
    200,
    114.26871681,
    36.50940896,
    200,
    114.26874142,
    36.50947827,
    200,
    114.26892598,
    36.50999802,
    200,
    114.26911053,
    36.51051778,
    200,
    114.26917756,
    36.51070654,
    200,
    114.26923802,
    36.51122689,
    200,
    114.26953947,
    36.51207583,
    200,
    114.26985429,
    36.51261232,
    200,
    114.27003336,
    36.51311657,
    200,
    114.27021793,
    36.51363633,
    200,
    114.2704025,
    36.51415609,
    200,
    114.27058708,
    36.51467584,
    200,
    114.27077166,
    36.5151956,
    200,
    114.27095624,
    36.51571536,
    200,
    114.27114083,
    36.51623511,
    200,
    114.27132542,
    36.51675487,
    200,
    114.27151001,
    36.51727462,
    200,
    114.2716946,
    36.51779438,
    200,
    114.27187919,
    36.51831413,
    200,
    114.27206379,
    36.51883388,
    200,
    114.27223182,
    36.51930697,
    200,
    114.27180253,
    36.51940631,
    200,
    114.27198713,
    36.51992606,
    200,
    114.27216884,
    36.52043764,
    200,
    114.27249704,
    36.52036422,
    200,
    114.27314258,
    36.5202198,
    200,
    114.2734858,
    36.52014303,
    200,
    114.27367042,
    36.52066279,
    200,
    114.27373483,
    36.52084414,
    200,
    114.27422284,
    36.52121455,
    200,
    114.27446985,
    36.52140203,
    200,
    114.27471084,
    36.52158495,
    200,
    114.27510125,
    36.52188127,
    200,
    114.27519885,
    36.52195535,
    200,
    114.27568687,
    36.52232575,
    200,
    114.27617489,
    36.52269615,
    200,
    114.27694973,
    36.52328422,
    200,
    114.27715095,
    36.52343694,
    200,
    114.27763898,
    36.52380733,
    200,
    114.27812702,
    36.52417772,
    200,
    114.27861506,
    36.5245481,
    200,
    114.27910311,
    36.52491849,
    200,
    114.27959117,
    36.52528887,
    200,
    114.27998928,
    36.52559099,
    200,
    114.28365738,
    36.52772178,
    200,
    114.28519161,
    36.52936075,
    200,
    114.28582156,
    36.5295448,
    200,
    114.28645151,
    36.52972885,
    200,
    114.28708146,
    36.5299129,
    200,
    114.28771141,
    36.53009694,
    200,
    114.28834137,
    36.53028098,
    200,
    114.28897134,
    36.53046501,
    200,
    114.2896013,
    36.53064905,
    200,
    114.28985332,
    36.53072267,
    200,
    114.28958702,
    36.53131638,
    200,
    114.29026056,
    36.53151314,
    200,
    114.29015415,
    36.53175039,
    200,
    114.29010854,
    36.53185207,
    200,
    114.29073852,
    36.5320361,
    200,
    114.2913685,
    36.53222012,
    200,
    114.29199849,
    36.53240414,
    200,
    114.29262847,
    36.53258816,
    200,
    114.29325846,
    36.53277217,
    200,
    114.29388846,
    36.53295618,
    200,
    114.29451845,
    36.53314019,
    200,
    114.29514845,
    36.53332419,
    200,
    114.29536205,
    36.53338658,
    200,
    114.29577846,
    36.53350819,
    200,
    114.29595089,
    36.53355688,
    200,
    114.29611672,
    36.53318709,
    200,
    114.29617756,
    36.53305142,
    200,
    114.29617889,
    36.53304847,
    200,
    114.2963858,
    36.53258706,
    200,
    114.29640688,
    36.53254005,
    200,
    114.29657271,
    36.53217026,
    200,
    114.29663355,
    36.53203459,
    200,
    114.29665831,
    36.53197937,
    200,
    114.2968863,
    36.53147095,
    200,
    114.29711429,
    36.53096253,
    200,
    114.29729124,
    36.53056793,
    200,
    114.2979251,
    36.53074302,
    200,
    114.29855897,
    36.53091811,
    200,
    114.29919285,
    36.53109319,
    200,
    114.29982672,
    36.53126827,
    200,
    114.3004606,
    36.53144334,
    200,
    114.30109449,
    36.53161841,
    200,
    114.30172837,
    36.53179348,
    200,
    114.30236226,
    36.53196854,
    200,
    114.30299615,
    36.5321436,
    200,
    114.30363004,
    36.53231866,
    200,
    114.30426394,
    36.53249371,
    200,
    114.30489784,
    36.53266876,
    200,
    114.30553174,
    36.53284381,
    200,
    114.30616564,
    36.53301886,
    200,
    114.30679955,
    36.5331939,
    200,
    114.30743346,
    36.53336893,
    200,
    114.30806738,
    36.53354397,
    200,
    114.30870129,
    36.533719,
    200,
    114.30933521,
    36.53389402,
    200,
    114.30995367,
    36.53406478,
    200,
    114.3106126,
    36.5340632,
    200,
    114.31128259,
    36.53406159,
    200,
    114.31195258,
    36.53405997,
    200,
    114.31262257,
    36.53405835,
    200,
    114.31329257,
    36.53405673,
    200,
    114.31396256,
    36.53405511,
    200,
    114.31463255,
    36.53405348,
    200,
    114.31530255,
    36.53405184,
    200,
    114.31597254,
    36.53405021,
    200,
    114.31621264,
    36.53404962,
    200,
    114.31684046,
    36.53423844,
    200,
    114.31746827,
    36.53442726,
    200,
    114.31809609,
    36.53461607,
    200,
    114.31872392,
    36.53480488,
    200,
    114.31935174,
    36.53499369,
    200,
    114.31997957,
    36.53518249,
    200,
    114.3206074,
    36.53537129,
    200,
    114.32123524,
    36.53556009,
    200,
    114.32186308,
    36.53574888,
    200,
    114.32249092,
    36.53593767,
    200,
    114.32311876,
    36.53612646,
    200,
    114.32374661,
    36.53631524,
    200,
    114.32437446,
    36.53650402,
    200,
    114.32500231,
    36.5366928,
    200,
    114.32563017,
    36.53688157,
    200,
    114.32625803,
    36.53707034,
    200,
    114.32688589,
    36.53725911,
    200,
    114.32751376,
    36.53744787,
    200,
    114.32814163,
    36.53763663,
    200,
    114.3287695,
    36.53782539,
    200,
    114.32939737,
    36.53801414,
    200,
    114.33000738,
    36.53819752,
    200,
    114.32982025,
    36.53860285,
    200,
    114.33038012,
    36.53877115,
    200,
    114.33051616,
    36.53881205,
    200,
    114.33114405,
    36.53900079,
    200,
    114.33162865,
    36.53914646,
    200,
    114.33155219,
    36.53931212,
    200,
    114.33185933,
    36.53940444,
    200,
    114.3312017,
    36.54082902,
    200,
    114.33157844,
    36.54094226,
    200,
    114.33103267,
    36.5421245,
    200,
    114.33044662,
    36.54194834,
    200,
    114.32985676,
    36.54322601,
    200,
    114.32787342,
    36.54752169,
    200,
    114.3281246,
    36.5475972,
    200,
    114.32742276,
    36.54911719,
    200,
    114.32717158,
    36.54904169,
    200,
    114.32517779,
    36.55335931,
    200,
    114.32255866,
    36.55903074,
    200,
    114.32264734,
    36.58039887,
    200,
    114.32331773,
    36.58039705,
    200,
    114.32398812,
    36.58039523,
    200,
    114.32465852,
    36.58039341,
    200,
    114.32519483,
    36.58039194,
    200,
    114.32532891,
    36.58039158,
    200,
    114.3259993,
    36.58038975,
    200,
    114.32666969,
    36.58038791,
    200,
    114.32734008,
    36.58038607,
    200,
    114.32801048,
    36.58038423,
    200,
    114.32868087,
    36.58038238,
    200,
    114.32935126,
    36.58038053,
    200,
    114.33002165,
    36.58037868,
    200,
    114.33069204,
    36.58037682,
    200,
    114.33136243,
    36.58037496,
    200,
    114.33203283,
    36.58037309,
    200,
    114.33242,
    36.58037201,
    200,
    114.33241789,
    36.57987836,
    200,
    114.33308336,
    36.57981301,
    200,
    114.33374884,
    36.57974767,
    200,
    114.33398549,
    36.57972443,
    200,
    114.33410379,
    36.5797169,
    200,
    114.33465381,
    36.5796819,
    200,
    114.3347721,
    36.57967438,
    200,
    114.33532212,
    36.57963937,
    200,
    114.33544041,
    36.57963185,
    200,
    114.33599043,
    36.57959684,
    200,
    114.33617364,
    36.57958518,
    200,
    114.33624468,
    36.58012282,
    200,
    114.33631572,
    36.58066045,
    200,
    114.33634194,
    36.58085971,
    200,
    114.33634881,
    36.58091198,
    200,
    114.33638514,
    36.58114774,
    200,
    114.33665125,
    36.58112107,
    200,
    114.33691675,
    36.58109066,
    200,
    114.33704915,
    36.58107359,
    200,
    114.33718116,
    36.58105466,
    200,
    114.33731274,
    36.58103387,
    200,
    114.33757589,
    36.58099229,
    200,
    114.33783993,
    36.58095442,
    200,
    114.33787514,
    36.58111412,
    200,
    114.33822716,
    36.58106363,
    200,
    114.33819194,
    36.58090393,
    200,
    114.33836795,
    36.58087869,
    200,
    114.33915742,
    36.58075394,
    200,
    114.33994407,
    36.5806181,
    200,
    114.3403388,
    36.58055573,
    200,
    114.34060101,
    36.58051045,
    200,
    114.341254,
    36.58038803,
    200,
    114.34151409,
    36.58033539,
    200,
    114.34216708,
    36.58021297,
    200,
    114.34282006,
    36.58009055,
    200,
    114.34308125,
    36.58004157,
    200,
    114.34334346,
    36.57999629,
    200,
    114.34369175,
    36.57993098,
    200,
    114.34373729,
    36.58008898,
    200,
    114.34399848,
    36.58004001,
    200,
    114.34395294,
    36.57988201,
    200,
    114.34425763,
    36.57982488,
    200,
    114.34451984,
    36.57977959,
    200,
    114.34569519,
    36.5795592,
    200,
    114.3459574,
    36.57951391,
    200,
    114.34661037,
    36.57939146,
    200,
    114.34700213,
    36.57931799,
    200,
    114.34726433,
    36.5792727,
    200,
    114.34765611,
    36.57919922,
    200,
    114.34830904,
    36.57907678,
    200,
    114.34876612,
    36.57899105,
    200,
    114.34881167,
    36.57914904,
    200,
    114.34907286,
    36.57910006,
    200,
    114.34902731,
    36.57894207,
    200,
    114.3491579,
    36.57891758,
    200,
    114.349289,
    36.57889493,
    200,
    114.34942057,
    36.57887412,
    200,
    114.34955214,
    36.57885332,
    200,
    114.35027525,
    36.57873707,
    200,
    114.35093307,
    36.57863306,
    200,
    114.35106509,
    36.57861411,
    200,
    114.3511975,
    36.57859702,
    200,
    114.3515293,
    36.57855897,
    200,
    114.35155761,
    36.57871956,
    200,
    114.35182309,
    36.57868912,
    200,
    114.35179478,
    36.57852853,
    200,
    114.3518612,
    36.57852091,
    200,
    114.35252645,
    36.57845415,
    200,
    114.35265977,
    36.57844267,
    200,
    114.35279332,
    36.57843307,
    200,
    114.35306041,
    36.57841387,
    200,
    114.35346105,
    36.57838507,
    200,
    114.35386227,
    36.57836191,
    200,
    114.35426391,
    36.57834441,
    200,
    114.35466587,
    36.57833255,
    200,
    114.35506802,
    36.57832636,
    200,
    114.35573839,
    36.57832546,
    200,
    114.35600654,
    36.57832511,
    200,
    114.35627466,
    36.57832852,
    200,
    114.35654277,
    36.57833194,
    200,
    114.35694494,
    36.57833706,
    200,
    114.35761495,
    36.57835505,
    200,
    114.35828494,
    36.57837303,
    200,
    114.35841898,
    36.57837663,
    200,
    114.35841137,
    36.57856111,
    200,
    114.35840629,
    36.5786841,
    200,
    114.35873742,
    36.57871861,
    200,
    114.35940224,
    36.57878805,
    200,
    114.35980114,
    36.5788297,
    200,
    114.36006708,
    36.57885748,
    200,
    114.36073191,
    36.5789269,
    200,
    114.36139514,
    36.57900568,
    200,
    114.36147318,
    36.57901494,
    200,
    114.36155074,
    36.57902416,
    200,
    114.36213641,
    36.57909371,
    200,
    114.362166,
    36.57909723,
    200,
    114.36279965,
    36.57917248,
    200,
    114.3634372,
    36.57924819,
    200,
    114.36346288,
    36.57925123,
    200,
    114.36410044,
    36.57932694,
    200,
    114.36412612,
    36.57932999,
    200,
    114.36451239,
    36.57937586,
    200,
    114.36455018,
    36.57916883,
    200,
    114.36562919,
    36.57919584,
    200,
    114.36616757,
    36.57919786,
    200,
    114.36630159,
    36.57919479,
    200,
    114.3667034,
    36.57917992,
    200,
    114.36750625,
    36.57913887,
    200,
    114.36763989,
    36.57913015,
    200,
    114.36777332,
    36.57911955,
    200,
    114.36830603,
    36.57906964,
    200,
    114.36857181,
    36.57904093,
    200,
    114.36883693,
    36.57900849,
    200,
    114.36896912,
    36.57899041,
    200,
    114.36975978,
    36.57887075,
    200,
    114.37028508,
    36.57878357,
    200,
    114.37041591,
    36.57875993,
    200,
    114.37106741,
    36.57863253,
    200,
    114.37172157,
    36.57851432,
    200,
    114.37185188,
    36.57848883,
    200,
    114.3723752,
    36.57839426,
    200,
    114.3728964,
    36.57829233,
    200,
    114.3729437,
    36.57844968,
    200,
    114.3732043,
    36.57839872,
    200,
    114.37315699,
    36.57824137,
    200,
    114.37367819,
    36.57813944,
    200,
    114.37433234,
    36.57802121,
    200,
    114.37498383,
    36.57789379,
    200,
    114.37550717,
    36.5777992,
    200,
    114.37639709,
    36.57762514,
    200,
    114.37654953,
    36.57759532,
    200,
    114.37694202,
    36.57752437,
    200,
    114.37759616,
    36.57740613,
    200,
    114.3782503,
    36.57728788,
    200,
    114.3785452,
    36.57723019,
    200,
    114.37932741,
    36.57705475,
    200,
    114.37993501,
    36.57695831,
    200,
    114.38020472,
    36.57690555,
    200,
    114.38046417,
    36.5768509,
    200,
    114.38110973,
    36.57670519,
    200,
    114.38175529,
    36.57655947,
    200,
    114.3818844,
    36.57653033,
    200,
    114.3825267,
    36.57637554,
    200,
    114.383169,
    36.57622075,
    200,
    114.38329746,
    36.5761898,
    200,
    114.38336134,
    36.57617341,
    200,
    114.38342227,
    36.57632799,
    200,
    114.38367781,
    36.57626247,
    200,
    114.38361688,
    36.57610789,
    200,
    114.38368077,
    36.57609151,
    200,
    114.38393769,
    36.57602959,
    200,
    114.38444877,
    36.57589854,
    200,
    114.38509106,
    36.57574374,
    200,
    114.38572988,
    36.57557992,
    200,
    114.38624371,
    36.57545608,
    200,
    114.38688254,
    36.57529225,
    200,
    114.38752138,
    36.57512842,
    200,
    114.38816366,
    36.5749736,
    200,
    114.38880593,
    36.57481878,
    200,
    114.38931698,
    36.57468771,
    200,
    114.38937792,
    36.57484229,
    200,
    114.38963345,
    36.57477675,
    200,
    114.38957592,
    36.57462148,
    200,
    114.39008641,
    36.57449842,
    200,
    114.39072585,
    36.57433627,
    200,
    114.39098138,
    36.57427073,
    200,
    114.39162364,
    36.57411589,
    200,
    114.39226246,
    36.57395204,
    200,
    114.39290472,
    36.57379719,
    200,
    114.39309639,
    36.57374803,
    200,
    114.39315734,
    36.5739026,
    200,
    114.39341286,
    36.57383706,
    200,
    114.39335191,
    36.57368248,
    200,
    114.39354353,
    36.57363333,
    200,
    114.39418578,
    36.57347848,
    200,
    114.39457113,
    36.57338557,
    200,
    114.39495441,
    36.57328725,
    200,
    114.39543691,
    36.57317091,
    200,
    114.39559666,
    36.57313239,
    200,
    114.39600123,
    36.5730286,
    200,
    114.3960622,
    36.57318317,
    200,
    114.39631771,
    36.57311762,
    200,
    114.39625676,
    36.57296305,
    200,
    114.39638452,
    36.57293027,
    200,
    114.39702677,
    36.57277541,
    200,
    114.39766558,
    36.57261152,
    200,
    114.39830781,
    36.57245665,
    200,
    114.3989466,
    36.57229276,
    200,
    114.39907505,
    36.57226178,
    200,
    114.3991055,
    36.57234395,
    200,
    114.39913265,
    36.57241719,
    200,
    114.39938954,
    36.57235524,
    200,
    114.39935975,
    36.57227486,
    200,
    114.39933194,
    36.57219983,
    200,
    114.39958883,
    36.57213788,
    200,
    114.40022761,
    36.57197398,
    200,
    114.40086984,
    36.5718191,
    200,
    114.40099759,
    36.57178632,
    200,
    114.40112462,
    36.57175174,
    200,
    114.40125088,
    36.57171538,
    200,
    114.40137633,
    36.57167726,
    200,
    114.40150094,
    36.57163737,
    200,
    114.40162467,
    36.57159573,
    200,
    114.40174927,
    36.57155584,
    200,
    114.40187473,
    36.57151771,
    200,
    114.40200098,
    36.57148135,
    200,
    114.40212801,
    36.57144677,
    200,
    114.40225576,
    36.57141399,
    200,
    114.40238421,
    36.57138301,
    200,
    114.4025133,
    36.57135385,
    200,
    114.40264301,
    36.5713265,
    200,
    114.40277327,
    36.57130099,
    200,
    114.40290408,
    36.57127731,
    200,
    114.40303538,
    36.57125548,
    200,
    114.40395766,
    36.57111562,
    200,
    114.40408982,
    36.57109749,
    200,
    114.40422236,
    36.57108123,
    200,
    114.40435523,
    36.57106684,
    200,
    114.40448839,
    36.57105432,
    200,
    114.4046218,
    36.57104367,
    200,
    114.40528991,
    36.57099985,
    200,
    114.40543207,
    36.57099265,
    200,
    114.40555046,
    36.57098824,
    200,
    114.40632636,
    36.57102125,
    200,
    114.40699572,
    36.57104973,
    200,
    114.40712098,
    36.57105505,
    200,
    114.40742272,
    36.57106789,
    200,
    114.40779031,
    36.57108353,
    200,
    114.40845964,
    36.57111199,
    200,
    114.40887256,
    36.57112955,
    200,
    114.40889942,
    36.5711307,
    200,
    114.40903346,
    36.57113262,
    200,
    114.40916752,
    36.57113171,
    200,
    114.4093015,
    36.57112797,
    200,
    114.40943533,
    36.5711214,
    200,
    114.40956888,
    36.571112,
    200,
    114.40970209,
    36.5710998,
    200,
    114.40983485,
    36.57108478,
    200,
    114.40996708,
    36.57106696,
    200,
    114.41009869,
    36.57104636,
    200,
    114.41022958,
    36.57102299,
    200,
    114.41035967,
    36.57099686,
    200,
    114.41048887,
    36.57096799,
    200,
    114.41061708,
    36.57093641,
    200,
    114.41074423,
    36.57090212,
    200,
    114.41149439,
    36.57068209,
    200,
    114.41171593,
    36.57088647,
    200,
    114.41189784,
    36.5710543,
    200,
    114.41181377,
    36.57113853,
    200,
    114.41173154,
    36.57122393,
    200,
    114.41157079,
    36.57139703,
    200,
    114.4114923,
    36.57148469,
    200,
    114.41141572,
    36.57157345,
    200,
    114.41111708,
    36.57193271,
    200,
    114.41104439,
    36.57202357,
    200,
    114.41097367,
    36.57211544,
    200,
    114.41090495,
    36.57220829,
    200,
    114.41063814,
    36.5725835,
    200,
    114.41057348,
    36.57267822,
    200,
    114.41051088,
    36.57277385,
    200,
    114.41045036,
    36.57287033,
    200,
    114.41038983,
    36.57296682,
    200,
    114.4103314,
    36.57306415,
    200,
    114.41027509,
    36.57316228,
    200,
    114.41022091,
    36.5732612,
    200,
    114.40995001,
    36.57375575,
    200,
    114.40979391,
    36.57405473,
    200,
    114.40974404,
    36.57415511,
    200,
    114.40969635,
    36.57425617,
    200,
    114.40962481,
    36.5744548,
    200,
    114.40945638,
    36.57497813,
    200,
    114.40937085,
    36.57524386,
    200,
    114.40935796,
    36.5752839,
    200,
    114.40928793,
    36.57550146,
    200,
    114.40911949,
    36.5760248,
    200,
    114.40895295,
    36.5765422,
    200,
    114.40882003,
    36.57707215,
    200,
    114.40876773,
    36.57728067,
    200,
    114.40865968,
    36.57780009,
    200,
    114.40856471,
    36.57836037,
    200,
    114.40849956,
    36.57889849,
    200,
    114.40847598,
    36.57909328,
    200,
    114.40844595,
    36.57944062,
    200,
    114.40842501,
    36.5797898,
    200,
    114.40841394,
    36.58004865,
    200,
    114.40840736,
    36.5805893,
    200,
    114.40840078,
    36.58112995,
    200,
    114.40839421,
    36.58167061,
    200,
    114.40839282,
    36.58178443,
    200,
    114.40838793,
    36.5823251,
    200,
    114.40838304,
    36.58286576,
    200,
    114.40837815,
    36.58340642,
    200,
    114.40837326,
    36.58394709,
    200,
    114.40836837,
    36.58448775,
    200,
    114.40836348,
    36.58502842,
    200,
    114.40835859,
    36.58556908,
    200,
    114.40835371,
    36.58610974,
    200,
    114.40834882,
    36.58665041,
    200,
    114.40834393,
    36.58719107,
    200,
    114.40833904,
    36.58773174,
    200,
    114.40833415,
    36.5882724,
    200,
    114.40832926,
    36.58881306,
    200,
    114.40832437,
    36.58935373,
    200,
    114.40831948,
    36.58989439,
    200,
    114.40831707,
    36.59016028,
    200,
    114.40831454,
    36.59044003,
    200,
    114.40831218,
    36.59070094,
    200,
    114.4083097,
    36.59097572,
    200,
    114.40830729,
    36.59124161,
    200,
    114.40830517,
    36.59147548,
    200,
    114.40830081,
    36.59201615,
    200,
    114.40829644,
    36.59255681,
    200,
    114.40829557,
    36.59266495,
    200,
    114.40829207,
    36.59309748,
    200,
    114.40828771,
    36.59363815,
    200,
    114.40828334,
    36.59417881,
    200,
    114.40827819,
    36.59476261,
    200,
    114.40827426,
    36.59530328,
    200,
    114.40826989,
    36.59584397,
    200,
    114.40819149,
    36.59627285,
    200,
    114.40818934,
    36.59655452,
    200,
    114.40818896,
    36.5966012,
    200,
    114.40818546,
    36.59704152,
    200,
    114.40818467,
    36.59714186,
    200,
    114.40818015,
    36.59770953,
    200,
    114.40818032,
    36.59825021,
    200,
    114.4081804,
    36.59847884,
    200,
    114.40818542,
    36.59879086,
    200,
    114.40818801,
    36.59895178,
    200,
    114.40819722,
    36.59933145,
    200,
    114.40820125,
    36.5994972,
    200,
    114.40823638,
    36.60003714,
    200,
    114.40827152,
    36.60057707,
    200,
    114.40828588,
    36.60079773,
    200,
    114.40834187,
    36.60133652,
    200,
    114.40836654,
    36.60157387,
    200,
    114.40844144,
    36.60211116,
    200,
    114.40851635,
    36.60264845,
    200,
    114.40859125,
    36.60318575,
    200,
    114.40866616,
    36.60372304,
    200,
    114.40874107,
    36.60426033,
    200,
    114.40881598,
    36.60479763,
    200,
    114.40889089,
    36.60533492,
    200,
    114.4089658,
    36.60587221,
    200,
    114.40904071,
    36.6064095,
    200,
    114.40911563,
    36.60694679,
    200,
    114.40919054,
    36.60748409,
    200,
    114.40926546,
    36.60802138,
    200,
    114.40931411,
    36.60837029,
    200,
    114.40945334,
    36.60889919,
    200,
    114.40956212,
    36.6093124,
    200,
    114.40915358,
    36.60937247,
    200,
    114.40917771,
    36.6094783,
    200,
    114.40923831,
    36.60974418,
    200,
    114.40939602,
    36.61043601,
    200,
    114.40943033,
    36.61058655,
    200,
    114.40962686,
    36.61088223,
    200,
    114.40962787,
    36.61107386,
    200,
    114.40962816,
    36.61112803,
    200,
    114.40960501,
    36.61112811,
    200
  ]
  // const arr3 = [
  //   114.34894227,
  //   36.57912455,
  //   200,
  //   114.3490223,
  //   36.57940213,
  //   200,
  //   114.2190189,
  //   36.45971637,
  //   200,
  //   114.21906552,
  //   36.45994766,
  //   200
  // ]

  const arr5 = [
    114.16739793,
    36.44984469,
    200,
    114.16819692,
    36.449641,
    200,
    114.16841447,
    36.44958525,
    200,
    114.1690226,
    36.44943002,
    200,
    114.1689666,
    36.4492865,
    200,
    114.16899695,
    36.44905344,
    200,
    114.17250031,
    36.44935111,
    200,
    114.17239679,
    36.45014644,
    200,
    114.17883711,
    36.45069338,
    200,
    114.17903591,
    36.44923349,
    200,
    114.18088268,
    36.44938752,
    200,
    114.18084332,
    36.4507462,
    200,
    114.18242059,
    36.45088926,
    200,
    114.18236238,
    36.4513369,
    200,
    114.18287354,
    36.45138028,
    200,
    114.18285878,
    36.45149379,
    200,
    114.1828448,
    36.45160134,
    200,
    114.18283082,
    36.45170885,
    200,
    114.18281684,
    36.4518164,
    200,
    114.18294963,
    36.45182992,
    200,
    114.18308241,
    36.45184344,
    200,
    114.18321521,
    36.45185696,
    200,
    114.18334799,
    36.45187047,
    200,
    114.18348079,
    36.45188399,
    200,
    114.18361357,
    36.45189751,
    200,
    114.18374637,
    36.45191103,
    200,
    114.18387915,
    36.45192455,
    200,
    114.18401195,
    36.45193807,
    200,
    114.18414473,
    36.45195159,
    200,
    114.18427757,
    36.45196511,
    200,
    114.18554007,
    36.45209363,
    200,
    114.18567292,
    36.45210715,
    200,
    114.18580579,
    36.45212067,
    200,
    114.19127261,
    36.45512921,
    200,
    114.19133525,
    36.45522478,
    200,
    114.19364567,
    36.45874953,
    200,
    114.19133525,
    36.45522478,
    200
  ]

  const arr6 = [
    114.1556795,
    36.46960952,
    200,
    114.15567865,
    36.4693856,
    200,
    114.15566989,
    36.46704981,
    200,
    114.15566949,
    36.46694167,
    200,
    114.15566908,
    36.46683353,
    200,
    114.15566868,
    36.46672539,
    200,
    114.15580256,
    36.46672506,
    200,
    114.15588431,
    36.46672486,
    200,
    114.15593644,
    36.46672474,
    200,
    114.15613787,
    36.46672424,
    200,
    114.15616881,
    36.46672417,
    200,
    114.15621344,
    36.46672406,
    200,
    114.15620522,
    36.46453676,
    200,
    114.15616059,
    36.46453686,
    200,
    114.15576083,
    36.46453784,
    200,
    114.15575901,
    36.46405073,
    200,
    114.15595983,
    36.46405024,
    200,
    114.15609371,
    36.46404991,
    200,
    114.15609331,
    36.46394177,
    200,
    114.1560929,
    36.46383363,
    200,
    114.15608375,
    36.46139729,
    200,
    114.15608334,
    36.46128915,
    200,
    114.15608293,
    36.46118101,
    200,
    114.15621681,
    36.46118068,
    200,
    114.15717852,
    36.46117832,
    200,
    114.15815705,
    36.45570491,
    200,
    114.15815988,
    36.45568906,
    200,
    114.15817618,
    36.45559788,
    200,
    114.15819531,
    36.45549085,
    200,
    114.15817351,
    36.45538415,
    200,
    114.15767241,
    36.45293215,
    200,
    114.15774147,
    36.45041645,
    200,
    114.16372652,
    36.45052352,
    200,
    114.16386035,
    36.45052592,
    200,
    114.16503515,
    36.45046497,
    200,
    114.16517847,
    36.45042623,
    200,
    114.16607026,
    36.45018519,
    200,
    114.16683589,
    36.44998921,
    200,
    114.16739813,
    36.44984464,
    200
  ]

  const arrLine: any[] = [];

  arrLine.push(addPipe1(viewer, arr1, 80, Color.fromCssColorString('#C0C0C0')))
  arrLine.push(addPipe1(viewer, arr2, 80, Color.fromCssColorString('#C0C0C0')))
  // addPipe(viewer, arr3, 80, Color.fromCssColorString('#C0C0C0'))
  // addPipe(viewer, arr4, 80, Color.fromCssColorString('#C0C0C0'))
  arrLine.push(addPipe1(viewer, arr5, 80, Color.fromCssColorString('#C0C0C0')))
  arrLine.push(addPipe1(viewer, arr6, 80, Color.fromCssColorString('#C0C0C0')))

  return arrLine
}

/**
 * @description: 生成3d圆形管道
 * 实现流动管道思路: 1. 管道半透明. 2.弄一个亮色的球体+一连串圆环, 一直顺着管道的坐标移动
 */
export function addPipe1(viewer: Viewer, positionsArr: number[], width: number, color: any) {
  const postions = Cartesian3.fromDegreesArrayHeights(positionsArr)

  // 2. 创建截面形状（圆形）
  function computeCircle(radius: number) {
    const shape = [];
    for (let i = 0; i < 36; i++) {
      const angle = (i / 36) * Math.PI * 2;
      shape.push(new Cartesian2(radius * Math.cos(angle), radius * Math.sin(angle)));
    }
    return shape;
  }
  const shape = computeCircle(width);

  // 2. 创建管道实体
  const pipeline = viewer.entities.add({
    name: '3D圆形管道',
    polylineVolume: {
      positions: postions,
      shape: shape, // 截面半径（单位：米）
      material: color, // 天蓝色材质
      cornerType: CornerType.ROUNDED, // 圆角过渡[1](@ref)
      granularity: CesiumMath.RADIANS_PER_DEGREE, // 控制路径插值精度[2](@ref)
    },
  })

  return pipeline
}

/**
 * @description: 生成3d圆形管道
 * 实现流动管道思路: 1. 管道半透明. 2.弄一个亮色的球体+一连串圆环, 一直顺着管道的坐标移动
 */
export function addPipe(viewer: Viewer, positionsArr: number[], width: number, color: any) {
  const postions = Cartesian3.fromDegreesArrayHeights(positionsArr)

  // 2. 创建截面形状（圆形）
  function computeCircle(radius: number) {
    const shape = [];
    for (let i = 0; i < 360; i++) {
      const angle = (i / 360) * Math.PI * 2;
      shape.push(new Cartesian2(radius * Math.cos(angle), radius * Math.sin(angle)));
    }
    return shape;
  }
  const shape = computeCircle(width);

  // 3. 使用Primitive创建管道
  const material = Color.RED; // 或者使用其他材质
  // const polylineVolumePrimitive = createPolylineVolumePrimitive(postions, shape, {
  //   color: material,
  //   translucent: false
  // });

  // 4. 添加到场景
  // viewer.scene.primitives.add(polylineVolumePrimitive);

  // 将路径分成多个段
  const segmentLength = 100; // 每段100个点
  const segments: any[] = [];
  if (postions.length <= 100) {
    segments.push(postions)
  } else {
    for (let i = 0; i < postions.length; i += segmentLength) {
      segments.push(postions.slice(i, i + segmentLength));
    }
  }
  console.log('%c [ segments ]-2784', 'font-size:13px; background:#862087; color:#ca64cb;', segments);

  // 分帧加载
  let currentSegment = 0;
  function addNextSegment() {
    if (currentSegment >= segments.length) return;

    const segmentPositions = segments[currentSegment];
    const primitive = createPolylineVolumePrimitive(segmentPositions, shape, {
      color: color,
      translucent: false
    });
    viewer.scene.primitives.add(primitive);

    currentSegment++;
    if (currentSegment < segments.length) {
      requestAnimationFrame(addNextSegment);
    }
  }

  addNextSegment();
}

/**
 * @description: 生成3d圆形管道(带流动特效)
 */
// export function addPipeFlow(viewer: Viewer) {
//   // 1. 定义圆形截面生成函数（核心参数：半径）
//   const computeCircle = (radius: number) => {
//     const positions = []
//     for (let i = 0; i < 360; i++) {
//       const radians = CesiumMath.toRadians(i)
//       positions.push(
//         new Cartesian2(
//           radius * Math.cos(radians), // X轴坐标
//           radius * Math.sin(radians), // Y轴坐标
//         ),
//       )
//     }
//     return positions // 返回二维点数组描述圆形截面[1,2](@ref)
//   }

//   // 创建自定义流动材质
//   class FlowingMaterialProperty implements MaterialProperty {
//     constructor(options: any = {}) {
//       this._color = undefined;
//       this._colorSubscription = undefined;
//       this._speed = undefined;
//       this._speedSubscription = undefined;
//       this.color = options.color || Color.fromCssColorString('#00BFFF').withAlpha(0.7);
//       this.speed = options.speed || 10;
//     }

//     _definitionChanged = new Event();
//     _color: any;
//     _colorSubscription: any;
//     _speed: any;
//     _speedSubscription: any;
//     color: any;
//     speed: any;

//     get isConstant() {
//       return false;
//     }

//     get definitionChanged() {
//       return this._definitionChanged;
//     }

//     getType() {
//       return 'FlowLine';
//     }

//     getValue(time: any, result: any) {
//       if (!result) {
//         result = {};
//       }
//       result.color = Property.getValueOrClonedDefault(this._color, time, Color.WHITE, result.color);
//       result.speed = Property.getValueOrDefault(this._speed, time, 10);
//       result.image = '/static/image/spriteline.png';
//       return result;
//     }

//     equals(other: any) {
//       return (
//         this === other ||
//         (other instanceof FlowingMaterialProperty &&
//           Property.equals(this._color, other._color) &&
//           Property.equals(this._speed, other._speed))
//       );
//     }
//   }

//   // 替换 Object.defineProperties 的方式
//   // 不使用 createPropertyDescriptor，直接定义 getter/setter
//   Object.defineProperties(FlowingMaterialProperty.prototype, {
//     color: {
//       get: function () {
//         return this._color;
//       },
//       set: function (value) {
//         if (this._color !== value) {
//           this._color = value;
//           this._definitionChanged.raiseEvent(this);
//         }
//       }
//     },
//     speed: {
//       get: function () {
//         return this._speed;
//       },
//       set: function (value) {
//         if (this._speed !== value) {
//           this._speed = value;
//           this._definitionChanged.raiseEvent(this);
//         }
//       }
//     }
//   });

//   // 注册材质
//   Material.FlowLineType = 'FlowLine';
//   Material._materialCache.addMaterial(Material.FlowLineType, {
//     fabric: {
//       type: Material.FlowLineType,
//       uniforms: {
//         color: new Color(1.0, 1.0, 1.0, 0.7),
//         speed: 10,
//         image: '/static/image/spriteline.png'
//       },
//       source: `
//         czm_material czm_getMaterial(czm_materialInput materialInput) {
//           czm_material material = czm_getDefaultMaterial(materialInput);
//           vec2 st = materialInput.st;
//           // 使用texture而不是texture2D
//           vec4 colorImage = texture(image, vec2(fract(st.s - speed * czm_frameNumber * 0.005), st.t));
//           material.alpha = colorImage.a * color.a;
//           material.diffuse = color.rgb * colorImage.rgb;
//           return material;
//         }
//       `
//     },
//     translucent: function (material) {
//       return true;
//     }
//   });

//   // 2. 创建管道实体
//   const pipeline = viewer.entities.add({
//     name: '3D圆形管道(流动特效)',
//     polylineVolume: {
//       positions: Cartesian3.fromDegreesArrayHeights([
//         120.728823, 31.255826, -50,
//         120.730903, 31.256668, 0,
//         120.732442, 31.254645, 50,
//         120.728823, 31.254645, 60,
//         120.728823, 31.255826, 70,
//         120.730903, 31.256668, 80,
//         120.732442, 31.254645, 90,
//         120.728823, 31.254645, 100,
//       ]),
//       shape: computeCircle(2.0),
//       cornerType: CornerType.ROUNDED,
//       granularity: CesiumMath.RADIANS_PER_DEGREE,
//       // 使用自定义流动材质
//       material: new FlowingMaterialProperty({
//         color: Color.fromCssColorString('#00BFFF').withAlpha(0.7),
//         speed: 2
//       })
//     }
//   });

//   return pipeline;
// }

/**
 * @description: 粒子系统-火焰
 */
export function addParticleFire(viewer: Viewer) {
  // 位置
  const planePosition = Cartesian3.fromDegrees(120.732442, 31.254645, 50)

  const entity = viewer.entities.add({
    //选择粒子放置的坐标
    position: planePosition,
  })

  viewer.scene.primitives.add(
    new ParticleSystem({
      image: '/static/image/fire.png',

      startColor: Color.RED,
      endColor: Color.YELLOW,
      startScale: 1.0,
      endScale: 3.0,
      particleLife: 1.5,
      speed: 10,
      imageSize: new Cartesian2(25, 25),

      emissionRate: 200.0,
      emitter: new ConeEmitter(CesiumMath.toRadians(45.0)), //BoxEmitter 盒形发射器，ConeEmitter 锥形发射器，SphereEmitter 球形发射器，CircleEmitter圆形发射器
      modelMatrix: entity.computeModelMatrix(viewer.clock.startTime, new Matrix4()),
    }),
  )
}

/**
 * @description: 平面流动道路，使用贴图实现
 */
export async function addPlaneFlowRoad(viewer: Viewer) {
  const instance: any = [];
  // 修改坐标数组，添加高度信息
  const positionArr = [
    120.728823, 31.255826, 50, // 添加10米高度
    120.738823, 31.255826, 50,
    120.738823, 31.265826, 50,
    120.728823, 31.265826, 50,
  ]

  const polyline = new PolylineGeometry({
    // 使用fromDegreesArrayHeights代替fromDegreesArray来支持高度
    positions: Cartesian3.fromDegreesArrayHeights(positionArr),
    width: 6,
    vertexFormat: PolylineMaterialAppearance.VERTEX_FORMAT,
  });
  const geometry: any = PolylineGeometry.createGeometry(polyline);
  instance.push(
    new GeometryInstance({
      geometry,
      // attributes: {
      //   color: ColorGeometryInstanceAttribute.fromColor(Color.RED),
      // },
    })
  );

  let source = `czm_material czm_getMaterial(czm_materialInput materialInput)
                  {
                      czm_material material = czm_getDefaultMaterial(materialInput);
                      vec2 st = materialInput.st;
                      vec4 colorImage = texture(image, vec2(fract((st.s - speed * czm_frameNumber * 0.001)), st.t));
                      material.alpha = colorImage.a * color.a;
                      material.diffuse = colorImage.rgb * 1.5 ;
                      return material;
                  }`

  const material = new Material({
    fabric: {
      uniforms: {
        color: Color.fromCssColorString('#7ffeff'),
        image: '/static/image/spriteline.png',
        speed: 10,
      },
      source,
    },
    translucent: function () {
      return true
    },
  })
  const appearance = new PolylineMaterialAppearance()
  appearance.material = material
  const primitive = new Primitive({
    geometryInstances: instance,
    appearance,
    asynchronous: false,
  })

  return viewer.scene.primitives.add(primitive)
}

/**
 * @description: 添加发光折线
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} coordinates - 坐标数组，格式为 [lon1, lat1, height1, lon2, lat2, height2, ...]
 * @param {Object} options - 可选配置项
 * @returns {Primitive} 返回创建的图元对象
 */
export function addGlowingPolyline(viewer: Viewer, coordinates: number[], options: any = {}) {
  // 默认配置
  const defaultOptions = {
    width: 10,                                    // 线宽
    glowPower: 0.25,                              // 发光强度，越小越亮
    taperPower: 1.0,                              // 锥度，控制线两端的渐变效果
    color: Color.fromCssColorString('#00ffff'),   // 线条颜色
    glowColor: Color.fromCssColorString('#00ffff') // 发光颜色
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...options };

  // 创建几何实例
  const instance = new GeometryInstance({
    geometry: new PolylineGeometry({
      positions: Cartesian3.fromDegreesArrayHeights(coordinates),
      width: finalOptions.width,
      vertexFormat: PolylineMaterialAppearance.VERTEX_FORMAT
    })
  });

  // 创建发光材质
  const material = new Material({
    fabric: {
      type: 'PolylineGlow',
      uniforms: {
        color: finalOptions.color,
        glowPower: finalOptions.glowPower,
        taperPower: finalOptions.taperPower
      }
    }
  });

  // 确保颜色正确应用
  material.uniforms.color = finalOptions.color;

  // 创建外观
  const appearance = new PolylineMaterialAppearance({
    material: material,
    translucent: true
  });

  // 创建图元并添加到场景
  const primitive = new Primitive({
    geometryInstances: instance,
    appearance: appearance,
    asynchronous: false
  });

  return viewer.scene.primitives.add(primitive);
}

/**
 * @description: 添加发光折线（替代方法）
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} coordinates - 坐标数组，格式为 [lon1, lat1, height1, lon2, lat2, height2, ...]
 * @param {Object} options - 可选配置项
 * @returns {Entity} 返回创建的实体对象
 */
export function addGlowingPolylineEntity(viewer: Viewer, coordinates: number[][], options?: {
  width?: number, // 线宽
  glowPower?: number, // 发光强度，越小越亮
  color?: string, // 线条颜色
}) {
  // 默认配置
  const defaultOptions = {
    width: 20,                                    // 线宽
    glowPower: 0.1,                              // 发光强度，越小越亮
    color: '#00ffff'   // 线条颜色
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...(options ?? {}) };

  // 将坐标数组转换为Cartesian3数组
  const coordsCart: Cartesian3[] = [];
  for (let i = 0; i < coordinates.length; i++) {
    coordsCart.push(Cartesian3.fromDegrees(
      coordinates[i][0],
      coordinates[i][1],
      coordinates[i][2]
    ));
  }


  // 使用Entity API创建发光折线
  return viewer.entities.add({
    polyline: {
      positions: coordsCart,
      width: finalOptions.width,
      // 使用 PolylineGlowMaterialProperty 替代 Material
      material: new PolylineGlowMaterialProperty({
        color: Color.fromCssColorString(finalOptions.color),
        glowPower: finalOptions.glowPower
      })
    }
  });
}

/**
 * 创建流光折线
 * @param {Array} positions 坐标数组（经纬度高程）
 * @param {Color} color 发光颜色
 * @param {number} width 线宽
 * @param {number} speed 流光速度, 值越大，速度越快
 * @param {number} glowPower 发光强度, 值越大，发光越亮
 */
export function createGlowingPolyline(viewer: Viewer, coordinates: number[][], options?: {
  color?: string,
  width?: number,
  speed?: number,
  glowPower?: number
}) {
  // 坐标转换（支持三维坐标）
  const cartesians = coordinates.map(p =>
    Cartesian3.fromDegrees(p[0], p[1], p[2] || 0)
  );

  // 默认配置
  const defaultOptions = {
    width: 10,
    speed: 6.5,
    color: '#00ffff',
    glowPower: 6
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...(options ?? {}) };

  // 创建自定义MaterialProperty类
  class GlowLineMaterialProperty implements MaterialProperty {
    private _definitionChanged = new Event();
    private _color: Color;
    private _glowPower: number;
    private _speed: number;
    private _time: number;
    private _startTime: JulianDate;

    constructor(color: Color, speed: number, glowPower: number) {
      this._color = color;
      this._speed = speed;
      this._glowPower = glowPower;
      this._time = 0.0;
      this._startTime = JulianDate.now(); // 记录开始时间
    }

    getType(): string {
      return 'WgFlowLight';
    }

    getValue(time: JulianDate, result?: any): any {
      if (!result) {
        result = {};
      }

      // 计算自开始以来的时间差（秒）
      // 使用 JulianDate.secondsDifference 而不是 toSeconds
      const currentTime = JulianDate.secondsDifference(time, this._startTime);
      this._time = currentTime;

      result.u_color = this._color;
      result.u_speed = this._speed;
      result.u_time = this._time;
      result.u_glowPower = this._glowPower;

      return result;
    }

    equals(other?: MaterialProperty): boolean {
      return (
        other instanceof GlowLineMaterialProperty &&
        this._color.equals((<GlowLineMaterialProperty>other)._color) &&
        this._speed === (<GlowLineMaterialProperty>other)._speed &&
        this._glowPower === (<GlowLineMaterialProperty>other)._glowPower
      );
    }

    get definitionChanged() {
      return this._definitionChanged;
    }

    get isConstant() {
      return false;
    }
  }

  // 注册材质
  (Material as any).WgFlowLightType = 'WgFlowLight';
  (Material as any)._materialCache.addMaterial((Material as any).WgFlowLightType, {
    fabric: {
      type: (Material as any).WgFlowLightType,
      uniforms: {
        u_color: Color.fromCssColorString(finalOptions.color),
        u_time: 0.0,
        u_speed: finalOptions.speed,
        u_glowPower: finalOptions.glowPower,
      },
      source: `
        czm_material czm_getMaterial(czm_materialInput materialInput) {
          czm_material material = czm_getDefaultMaterial(materialInput);

          // 计算流动光效（基于纹理坐标）
          float progress = fract(u_time * u_speed * 0.1 + materialInput.st.s);
          float glow = smoothstep(0.3, 0.7, abs(progress - 0.5));

          // 设置自发光颜色（emission属性决定发光强度）
          material.emission = u_color.rgb * glow * u_glowPower;
          material.alpha = u_color.a * glow;
          return material;
        }
      `
    },
    translucent: function () {
      return true;
    }
  });

  // 创建折线实体，使用自定义MaterialProperty
  const entity = viewer.entities.add({
    polyline: {
      positions: cartesians,
      width: finalOptions.width,
      material: new GlowLineMaterialProperty(Color.fromCssColorString(finalOptions.color), finalOptions.speed, finalOptions.glowPower)
    }
  });

  return entity;
}

/**
 * @description: 创建带发光边缘的地面多边形
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} coordinates - 坐标数组，格式为 [lon1, lat1, lon2, lat2, ...]（不需要高度）
 * @param {Object} options - 可选配置项
 * @returns {Entity[]} 返回创建的实体对象数组
 */
export function addGlowingPolygon(viewer: Viewer, coordinates: number[], options: any = {}) {
  // 默认配置
  const defaultOptions = {
    polygonColor: Color.fromCssColorString('#00ffff').withAlpha(0.5), // 多边形填充颜色
    outlineColor: Color.fromCssColorString('#80FFFF'),                // 边缘线颜色
    glowColor: Color.fromCssColorString('#afFFFF'),                   // 发光颜色
    outlineWidth: 2,                                                  // 边缘线宽度
    glowWidth: 80,                                                    // 发光宽度
    glowPower: 0.1,                                                  // 发光强度（越小越亮）
    height: 0                                                       // 高度偏移（米）
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...options };
  const entities: any[] = [];

  // 创建多边形坐标数组（无高度）
  const polygonPositions = [];
  for (let i = 0; i < coordinates.length; i += 2) {
    polygonPositions.push(coordinates[i], coordinates[i + 1]);
  }

  // 创建边缘线坐标数组（带高度）
  const outlinePositions = [];
  for (let i = 0; i < coordinates.length; i += 2) {
    outlinePositions.push(
      coordinates[i],
      coordinates[i + 1],
      finalOptions.height
    );
  }
  // 闭合轮廓
  outlinePositions.push(
    coordinates[0],
    coordinates[1],
    finalOptions.height
  );

  // 1. 添加基础多边形（填充区域）
  const polygon = viewer.entities.add({
    polygon: {
      hierarchy: Cartesian3.fromDegreesArray(polygonPositions),
      material: finalOptions.polygonColor,
      outline: true,
      outlineColor: finalOptions.outlineColor,
      outlineWidth: finalOptions.outlineWidth,
      height: 0,
      zIndex: 0
    }
  });
  entities.push(polygon);

  // 2. 添加发光边缘线（使用PolylineGlowMaterialProperty）
  const glowingOutline = viewer.entities.add({
    polyline: {
      positions: Cartesian3.fromDegreesArrayHeights(outlinePositions),
      width: finalOptions.glowWidth,
      material: new PolylineGlowMaterialProperty({
        color: finalOptions.glowColor,
        glowPower: finalOptions.glowPower
      }),
      clampToGround: false,
      zIndex: 1
    }
  });
  entities.push(glowingOutline);

  return entities;
}

/**
 * @description: 创建地面旋转图标
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} position - 位置坐标，格式为 [lon, lat, height(可选)]
 * @param {Object} options - 可选配置项
 * @returns {Object} 返回创建的实体对象和控制函数
 */
export function addRotatingGroundImage(viewer: Viewer) {
  let rotation = CesiumMath.toRadians(180);

  function getRotationValue() {
    rotation += 0.115;
    return rotation;
  }

  // 使用ImageMaterialProperty来正确处理透明度
  const imageMaterial = new ImageMaterialProperty({
    image: "/static/scan_blue.png",
    transparent: true,  // 启用透明度
    color: Color.WHITE.withAlpha(1.0)  // 保持图片原色，但可以调整整体透明度
  });

  // 定义位置和高度
  const longitude = 120.729823;
  const latitude = 31.256826;
  const height = 170;
  const baseHeight = height + 10;

  // 添加椭圆
  const ellipse = viewer.entities.add({
    position: Cartesian3.fromDegrees(longitude, latitude, 0),
    name: "监测点A",
    ellipse: {
      material: imageMaterial,
      semiMinorAxis: 20.0,
      semiMajorAxis: 20.0,
      height: height,
      outline: true,
      rotation: new CallbackProperty(getRotationValue, false),
      stRotation: new CallbackProperty(getRotationValue, false),
      classificationType: ClassificationType.TERRAIN,
    }
  });

  // 添加棱镜图标
  const prism = viewer.entities.add({
    position: Cartesian3.fromDegrees(longitude, latitude, baseHeight),
    name: "监测点A",
    billboard: {
      image: "/static/prism_blue.png",
      width: 20,
      height: 20,
      sizeInMeters: true,
    },
  });

  // 该平面不会始终面相用户，会随地图旋转
  // const skyPlane = viewer.entities.add({
  //   name: "垂直于地面的平面",
  //   position: Cartesian3.fromDegrees(120.729823, 31.256826, 50),
  //   plane: {
  //     plane: new Plane(Cartesian3.UNIT_Y, 0), // Y轴作为法线，使平面垂直于地面
  //     dimensions: new Cartesian2(100, 100),
  //     material: imageMaterial
  //   }
  // });

  // 为triangle添加上下浮动效果
  const floatAmplitude = 5; // 浮动幅度（米）
  const floatPeriod = 2; // 浮动周期（秒）
  const startTime = JulianDate.now(); // 记录开始时间

  // 创建位置回调函数，实现上下浮动
  const getFloatingPosition = new CallbackPositionProperty((time) => {
    // 计算自开始以来的时间（秒）
    const seconds = JulianDate.secondsDifference(time!, startTime);
    // 使用正弦函数计算当前高度偏移
    const heightOffset = floatAmplitude * Math.sin((seconds * Math.PI * 2) / floatPeriod);
    // 返回新的位置
    return Cartesian3.fromDegrees(longitude, latitude, baseHeight + heightOffset);
  }, false);

  // 添加三角形图标
  const triangle = viewer.entities.add({
    position: getFloatingPosition,
    name: "监测点A",
    billboard: {
      image: "/static/triangle_blue.png",
      width: 20,
      height: 20,
      sizeInMeters: true,
    },
  });

  // 创建HTML覆盖层
  createInfoOverlay(viewer, longitude, latitude, baseHeight + 15, {
    name: "监测点A",
    pressure: "1.5 MPa",
    flow: "25 m³/h",
    temperature: "28°C"
  });

  return { ellipse, prism, triangle };
}

/**
 * 创建信息覆盖层
 * @param viewer Cesium Viewer实例
 * @param longitude 经度
 * @param latitude 纬度
 * @param height 高度
 * @param info 要显示的信息对象
 */
function createInfoOverlay(viewer: Viewer, longitude: number, latitude: number, height: number, info: any) {
  // 创建HTML元素
  const overlayDiv = document.createElement('div');
  overlayDiv.className = 'cesium-info-overlay';
  overlayDiv.innerHTML = `
    <div class="cesium-info-card">
      <div class="cesium-info-title">${info.name}</div>
      <div class="cesium-info-content">
        <div class="cesium-info-item">压力: ${info.pressure}</div>
        <div class="cesium-info-item">流量: ${info.flow}</div>
        <div class="cesium-info-item">温度: ${info.temperature}</div>
      </div>
    </div>
  `;

  // 设置样式
  const style = document.createElement('style');
  style.textContent = `
    .cesium-info-overlay {
      position: absolute;
      pointer-events: none;
      z-index: 1;
      transform: translate(-50%, -100%);
    }
    .cesium-info-card {
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 5px;
      color: white;
      padding: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
      font-family: Arial, sans-serif;
      min-width: 150px;
    }
    .cesium-info-title {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 5px;
      color: #00ffff;
      text-align: center;
    }
    .cesium-info-content {
      font-size: 12px;
    }
    .cesium-info-item {
      margin: 3px 0;
    }
  `;
  document.head.appendChild(style);

  // 添加到DOM
  viewer.container.appendChild(overlayDiv);

  // 更新位置函数
  function updatePosition() {
    const position = Cartesian3.fromDegrees(longitude, latitude, height);
    const canvasPosition = viewer.scene.cartesianToCanvasCoordinates(position);

    if (defined(canvasPosition)) {
      overlayDiv.style.left = canvasPosition.x + 'px';
      overlayDiv.style.top = canvasPosition.y + 'px';
      overlayDiv.style.display = 'block';
    } else {
      overlayDiv.style.display = 'none';
    }
  }

  // 初始更新位置
  updatePosition();

  // 添加事件监听器，在相机移动时更新位置
  viewer.scene.postRender.addEventListener(updatePosition);

  // 返回清理函数
  return function cleanup() {
    viewer.container.removeChild(overlayDiv);
    viewer.scene.postRender.removeEventListener(updatePosition);
    document.head.removeChild(style);
  };
}
