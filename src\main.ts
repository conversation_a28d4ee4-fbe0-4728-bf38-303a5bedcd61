/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-02-21
 * @Description:
 * @FilePath: \demo2\src\main.ts
 */
import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// The URL on your server where CesiumJS's static files are hosted.
(window as any).CESIUM_BASE_URL = '/static/Cesium/';

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
