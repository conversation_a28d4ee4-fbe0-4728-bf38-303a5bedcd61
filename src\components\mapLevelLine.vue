<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-05
 * @Description: 初始化cesium, 并添加了自带的3d建筑图层
 * @FilePath: \demo2\src\components\mapLevelLine.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import 'cesium/Build/Cesium/Widgets/widgets.css'
import {
  Cartesian3,
  createOsmBuildingsAsync,
  Ion,
  Terrain,
  Viewer,
  ScreenSpaceEventHandler,
  ScreenSpaceEventType,
  defined,
  Cartographic,
  Math as CesiumMath,
  Cesium3DTileset,
  Color,
  NearFarScalar,
  Cartesian2,
} from 'cesium'
import { onMounted, onBeforeUnmount } from 'vue'
import {
  load3DTile,
  addPipeDemo,
  printPosition,
  addParticleFire,
  addPlaneFlowRoad,
  addGlowingPolyline,
  addGlowingPolylineEntity,
  createGlowingPolyline,
  addGlowingPolygon,
  addRotatingGroundImage,
} from './mapLevelLine'
import { useFence } from './demo0Fence'
import { CircleDiffusion } from './demo0Diffuse'

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg'

let viewer: Viewer
let tileset: Cesium3DTileset | undefined

onMounted(() => {
  initCesiumMap()
})

onBeforeUnmount(() => {
  if (viewer) viewer.destroy()
  if (tileset) tileset.destroy()
})

/**
 * @description: 实例化cesium
 */
async function initCesiumMap() {
  // 初始化地图并加载地形图
  viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
    shouldAnimate: true,

    timeline: false, // 直接关闭时间轴控件[2,5](@ref)
    animation: false, // 若需同时隐藏左下角动画控件
    // 隐藏右上角工具栏组件
    fullscreenButton: false, // 全屏按钮
    vrButton: false, // VR模式切换
    sceneModePicker: false, // 2D/3D/哥伦布视图切换
    baseLayerPicker: false, // 底图选择器（若需保留影像切换功能可不设置）
    geocoder: false, // 搜索框（位于左上角，但常与工具栏联动）
    homeButton: false, // 返回主视图按钮
    navigationHelpButton: false, // 帮助信息按钮
  })
  ;(viewer.cesiumWidget.creditContainer as HTMLDivElement).style.display = 'none' // 隐藏版权信息

  viewer.scene.globe.depthTestAgainstTerrain = true // 启用地形深度检测
  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false

  viewer.scene.globe.translucency.enabled = true // 启用地球半透模式
  viewer.scene.globe.translucency.frontFaceAlpha = 0.6 // 正面透明度

  // 缩到面前时透明，远了就不透明
  // viewer.scene.globe.translucency.frontFaceAlphaByDistance = new NearFarScalar(
  //       400.0,
  //       0.0,
  //       800.0,
  //       1.0
  //   );

  viewer.scene.globe.enableLighting = false // 关闭全局光照，降低计算量
  // 关闭抗锯齿（FXAA）或阴影效果（如果启用）。
  viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.shadows = false
  viewer.scene.debugShowFramesPerSecond = true // 显示帧率

  // 1米内的剔除；防止模型闪烁
  viewer.camera.frustum.near = 1

  // 根据地图等级控制渲染
  let pipes: any = null

  // 添加管道
  pipes = addPipeDemo(viewer)

  const updateRenderingByZoom = () => {
    // 根据相机高度计算管道半径，保持视觉直径一致
    const cameraHeight = viewer.camera.positionCartographic.height
    console.log(
      '%c [ cameraHeight ]-119',
      'font-size:13px; background:#e58077; color:#ffc4bb;',
      cameraHeight,
    )
    // 基准：在1000米高度时，半径为2米的视觉效果
    const baseHeight = 200
    const baseRadius = 2
    let visualRadius = (cameraHeight / baseHeight) * baseRadius
    if (visualRadius > 266) visualRadius = 266

    // 1. 定义圆形截面生成函数（核心参数：半径）
    function computeCircle(radius: number) {
      const shape: any[] = []
      for (let i = 0; i < 36; i++) {
        const angle = (i / 36) * Math.PI * 2
        shape.push(new Cartesian2(radius * Math.cos(angle), radius * Math.sin(angle)))
      }
      return shape
    }

    pipes.map((item: any) => (item.polylineVolume.shape = computeCircle(visualRadius)))
  }

  // 初始渲染
  updateRenderingByZoom()

  // 监听相机变化
  viewer.camera.changed.addEventListener(updateRenderingByZoom)

  try {
    viewer.camera.flyTo({
      destination: Cartesian3.fromDegrees(114.31076756194841, 36.49664990956251, 75482.27969285286),
    })
  } catch (error) {
    console.warn('自动定位到模型失败，使用默认位置:', error)
    // 使用手动设置的相机位置作为备选
    viewer.camera.flyTo({
      destination: Cartesian3.fromDegrees(116.4074, 39.9042, 5000), // 默认位置（北京）
    })
  }

  // 输出点击位置的坐标
  printPosition(viewer)
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
