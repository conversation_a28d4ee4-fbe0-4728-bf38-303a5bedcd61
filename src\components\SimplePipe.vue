<!--
 * @Author: Strayer
 * @Date: 2025-11-11
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-11
 * @Description: 简化版3D流动管道组件
 * @FilePath: \demo2\src\components\SimplePipe.vue
-->
<template>
  <div class="simple-pipe-container">
    <div id="cesiumContainer" class="cesium-container"></div>
    <div class="control-panel">
      <h3>3D流动管道控制面板</h3>
      <div class="control-group">
        <label>管道半径:</label>
        <input 
          type="range" 
          min="1" 
          max="10" 
          step="0.5" 
          v-model="pipeRadius" 
          @input="updatePipe"
        />
        <span>{{ pipeRadius }}m</span>
      </div>
      <div class="control-group">
        <label>透明度:</label>
        <input 
          type="range" 
          min="0.1" 
          max="1" 
          step="0.1" 
          v-model="opacity" 
          @input="updatePipe"
        />
        <span>{{ opacity }}</span>
      </div>
      <div class="control-group">
        <label>管道颜色:</label>
        <select v-model="pipeColor" @change="updatePipe">
          <option value="#00BFFF">天蓝色</option>
          <option value="#00FF00">绿色</option>
          <option value="#FF0000">红色</option>
          <option value="#FFFF00">黄色</option>
          <option value="#FF00FF">紫色</option>
          <option value="#00FFFF">青色</option>
        </select>
      </div>
      <div class="control-group">
        <button @click="togglePipe" class="toggle-btn">
          {{ showPipe ? '隐藏管道' : '显示管道' }}
        </button>
      </div>
      <div class="control-group">
        <button @click="addFlowingBall" class="action-btn">
          添加流动小球
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'cesium/Build/Cesium/Widgets/widgets.css'
import {
  Cartesian3,
  Ion,
  Terrain,
  Viewer,
  Math as CesiumMath,
  Color,
  CornerType,
  Cartesian2,
  JulianDate,
  CallbackPositionProperty,
  SampledPositionProperty,
  VelocityOrientationProperty,
  ClockRange,
  ClockStep,
} from 'cesium'
import { onMounted, onBeforeUnmount, ref } from 'vue'

// Your access token can be found at: https://ion.cesium.com/tokens.
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg'

let viewer: Viewer
let currentPipe: any = null
let flowingBalls: any[] = []

// 响应式数据
const pipeRadius = ref(3)
const opacity = ref(0.7)
const pipeColor = ref('#00BFFF')
const showPipe = ref(true)

// 管道路径坐标
const pipeCoordinates = [
  [120.727823, 31.266826, 10],
  [120.737823, 31.266826, 15],
  [120.737823, 31.276826, 20],
  [120.727823, 31.276826, 25],
  [120.720823, 31.276826, 30],
  [120.720823, 31.266826, 35],
]

onMounted(() => {
  initCesiumMap()
})

onBeforeUnmount(() => {
  if (viewer) viewer.destroy()
})

/**
 * @description: 初始化Cesium地图
 */
async function initCesiumMap() {
  // 初始化地图
  viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
    shouldAnimate: true,
    timeline: false,
    animation: false,
    fullscreenButton: false,
    vrButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    navigationHelpButton: false,
  })
  
  // 隐藏版权信息
  ;(viewer.cesiumWidget.creditContainer as HTMLDivElement).style.display = 'none'

  // 启用地形深度检测
  viewer.scene.globe.depthTestAgainstTerrain = true
  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false

  // 启用地球半透模式
  viewer.scene.globe.translucency.enabled = true
  viewer.scene.globe.translucency.frontFaceAlpha = 0.9

  viewer.scene.globe.enableLighting = false
  viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.shadows = false
  viewer.scene.debugShowFramesPerSecond = true

  // 1米内的剔除；防止模型闪烁
  viewer.camera.frustum.near = 1

  // 设置相机位置
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(120.732823, 31.271826, 200),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-45.0),
    }
  })

  // 创建初始管道
  createPipe()
}

/**
 * @description: 定义圆形截面生成函数
 */
function computeCircle(radius: number) {
  const positions = []
  for (let i = 0; i < 360; i++) {
    const radians = CesiumMath.toRadians(i)
    positions.push(
      new Cartesian2(
        radius * Math.cos(radians),
        radius * Math.sin(radians),
      ),
    )
  }
  return positions
}

/**
 * @description: 创建3D管道
 */
function createPipe() {
  if (currentPipe) {
    viewer.entities.remove(currentPipe)
    currentPipe = null
  }

  if (showPipe.value) {
    // 将坐标数组转换为平坦数组
    const positions = []
    for (let i = 0; i < pipeCoordinates.length; i++) {
      positions.push(pipeCoordinates[i][0], pipeCoordinates[i][1], pipeCoordinates[i][2])
    }

    currentPipe = viewer.entities.add({
      name: '3D圆形管道',
      polylineVolume: {
        positions: Cartesian3.fromDegreesArrayHeights(positions),
        shape: computeCircle(pipeRadius.value),
        material: Color.fromCssColorString(pipeColor.value).withAlpha(opacity.value),
        cornerType: CornerType.ROUNDED,
        granularity: CesiumMath.RADIANS_PER_DEGREE,
      },
    })
  }
}

/**
 * @description: 更新管道
 */
function updatePipe() {
  createPipe()
}

/**
 * @description: 切换管道显示/隐藏
 */
function togglePipe() {
  showPipe.value = !showPipe.value
  createPipe()
}

/**
 * @description: 添加流动小球
 */
function addFlowingBall() {
  // 创建位置属性
  const positionProperty = new SampledPositionProperty()
  
  // 设置时间范围
  const start = JulianDate.now()
  const stop = JulianDate.addSeconds(start, 30, new JulianDate()) // 30秒完成一次循环
  
  // 沿管道路径添加位置样本
  for (let i = 0; i < pipeCoordinates.length; i++) {
    const time = JulianDate.addSeconds(start, (i / (pipeCoordinates.length - 1)) * 30, new JulianDate())
    const position = Cartesian3.fromDegrees(
      pipeCoordinates[i][0],
      pipeCoordinates[i][1],
      pipeCoordinates[i][2]
    )
    positionProperty.addSample(time, position)
  }
  
  // 设置插值算法
  positionProperty.setInterpolationOptions({
    interpolationDegree: 2,
    interpolationAlgorithm: undefined
  })

  // 创建流动小球
  const ball = viewer.entities.add({
    name: '流动小球',
    position: positionProperty,
    point: {
      pixelSize: 15,
      color: Color.YELLOW,
      outlineColor: Color.BLACK,
      outlineWidth: 2,
      heightReference: undefined,
    },
    // 可选：添加标签
    label: {
      text: '流动',
      font: '12pt sans-serif',
      fillColor: Color.WHITE,
      outlineColor: Color.BLACK,
      outlineWidth: 2,
      style: undefined,
      pixelOffset: new Cartesian2(0, -40),
    }
  })

  flowingBalls.push(ball)

  // 设置时钟
  viewer.clock.startTime = start
  viewer.clock.stopTime = stop
  viewer.clock.currentTime = start
  viewer.clock.clockRange = ClockRange.LOOP_STOP
  viewer.clock.clockStep = ClockStep.SYSTEM_CLOCK_MULTIPLIER
  viewer.clock.multiplier = 1
}
</script>

<style scoped>
.simple-pipe-container {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 250px;
  font-family: Arial, sans-serif;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #00ffff;
  text-align: center;
}

.control-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-group label {
  font-size: 14px;
  margin-right: 10px;
  min-width: 80px;
}

.control-group input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.control-group select {
  flex: 1;
  padding: 5px;
  border-radius: 5px;
  border: none;
  background-color: #333;
  color: white;
}

.control-group span {
  min-width: 40px;
  text-align: right;
  font-size: 12px;
}

.toggle-btn, .action-btn {
  width: 100%;
  padding: 10px;
  background-color: #00ffff;
  color: black;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
  margin-bottom: 5px;
}

.toggle-btn:hover, .action-btn:hover {
  background-color: #00cccc;
}

.action-btn {
  background-color: #ff6600;
}

.action-btn:hover {
  background-color: #cc5500;
}
</style>
