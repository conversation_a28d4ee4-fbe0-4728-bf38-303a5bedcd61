<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-04-08
 * @Description: 初始化cesium, 并添加了自带的3d建筑图层
 * @FilePath: \demo2\src\components\demo7.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import "cesium/Build/Cesium/Widgets/widgets.css";
import {
  Cartesian3, createOsmBuildingsAsync, Ion, Math as CesiumMath, Terrain, Viewer, ScreenSpaceEventHandler,
  ScreenSpaceEventType,
  Color,
  JulianDate,
  PathGraphics,
  SampledPositionProperty,
  TimeInterval,
  TimeIntervalCollection,
  HeadingPitchRoll,
  Quaternion,
  Transforms,
  Matrix4,
  Cartographic
} from 'cesium';
import { onMounted } from 'vue';

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg';

onMounted(() => {
  initCesiumMap();
})

/**
 * @description: 实例化cesium
 */
async function initCesiumMap() {
  // Initialize the Cesium Viewer in the HTML element with the `cesiumContainer` ID.
  const viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
  });

  viewer.scene.globe.translucency.enabled = true; // 启用地球半透模式
  viewer.scene.globe.translucency.frontFaceAlpha = 0.9; // 正面透明度

  // Fly the camera to San Francisco at the given longitude, latitude, and height.
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(-122.39053, 37.61779, 1400),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-90.0),
    }
  });

  // 输出点击位置的坐标
  var handler = new ScreenSpaceEventHandler(viewer.canvas);
  handler.setInputAction(function (event: any) {
    var pickedPosition = viewer.scene.pickPosition(event.position);
    console.log(pickedPosition);

  }, ScreenSpaceEventType.LEFT_CLICK);


  const coordData = [
    { "longitude": -122.39053, "latitude": 37.61779, "height": 27.32 },
    { "longitude": -122.39035, "latitude": 37.61803, "height": 27.32 },
    { "longitude": -122.39019, "latitude": 37.61826, "height": 27.32 },
    { "longitude": -122.39006, "latitude": 37.6185, "height": 27.32 },
    { "longitude": -122.38985, "latitude": 37.61864, "height": 27.32 },
    { "longitude": -122.39005, "latitude": 37.61874, "height": 27.32 },
    { "longitude": -122.39027, "latitude": 37.61884, "height": 27.32 },
    { "longitude": -122.39057, "latitude": 37.61898, "height": 27.32 },
    { "longitude": -122.39091, "latitude": 37.61912, "height": 27.32 },
    { "longitude": -122.39121, "latitude": 37.61926, "height": 27.32 },
    { "longitude": -122.39153, "latitude": 37.61937, "height": 27.32 },
    { "longitude": -122.39175, "latitude": 37.61948, "height": 27.32 },
    { "longitude": -122.39207, "latitude": 37.6196, "height": 27.32 },
    { "longitude": -122.39247, "latitude": 37.61983, "height": 27.32 },
    { "longitude": -122.39253, "latitude": 37.62005, "height": 27.32 },
    { "longitude": -122.39226, "latitude": 37.62048, "height": 27.32 },
    { "longitude": -122.39207, "latitude": 37.62075, "height": 27.32 },
    { "longitude": -122.39186, "latitude": 37.62106, "height": 27.32 },
    { "longitude": -122.39172, "latitude": 37.62127, "height": 27.32 },
    { "longitude": -122.39141, "latitude": 37.62174, "height": 27.32 }
  ]

  for (let i = 0; i < coordData.length; i++) {
    const dataPoint = coordData[i];

    const position = Cartesian3.fromDegrees(dataPoint.longitude, dataPoint.latitude, dataPoint.height);

    viewer.entities.add({
      description: `Location: (${dataPoint.longitude}, ${dataPoint.latitude}, ${dataPoint.height})`,
      position: position,
      point: { pixelSize: 10, color: Color.RED }
    });
  }

  // 循环运动
  moveBoxAlongPath(viewer, coordData, 2000, true, 5);
}

/**
 * 使盒子沿路径匀速移动
 * @param viewer Cesium Viewer实例
 * @param entity 要移动的实体
 * @param pathData 路径数据数组
 * @param duration 移动总时长(毫秒)，默认10秒
 * @param loop 是否循环运动，默认false
 */
function moveBoxAlongPath(
  viewer: Viewer,
  pathData: any[],
  duration: number = 10000,
  loop: boolean = false,
  tailNum: number // 尾巴数量
) {
  // 计算每个点的时间间隔
  const startTime = JulianDate.fromDate(new Date());
  const timeStep = duration / (pathData.length - 1);
  const tailOffDistance = 10; // 尾巴每点偏移
  const radii = 8; // 直径
  const entityArr: any[] = [];
  const materialColor = '#00ffff'

  for (let i = 0; i < tailNum; i++) {
    const entity = viewer.entities.add({
      name: "Blue box " + i,
      ellipsoid: {
        radii: new Cartesian3(radii, radii, radii),
        material: Color.fromCssColorString(materialColor).withAlpha(0.9),
      },
    });
    entityArr.push(entity); // 只在这里添加一次实体到数组

    // 创建采样位置属性
    const positionProperty = new SampledPositionProperty();

    // 添加路径点
    pathData.forEach((point, index) => {
      const time = JulianDate.addSeconds(
        startTime,
        index * timeStep / 1000,
        new JulianDate()
      );
      let itemCoord = pathData[index];

      // 修改偏移计算逻辑
      if (i > 0) {
        // 对第一个点也进行偏移计算
        if (index === 0) {
          // 第一个点使用第一个和第二个点来计算方向
          itemCoord = pathData[0];
        } else {
          itemCoord = moveAlongVector(
            pathData[index],
            pathData[index - 1], // 确保索引有效
            tailOffDistance * i
          );
        }
      }

      let position = Cartesian3.fromDegrees(
        itemCoord.longitude,
        itemCoord.latitude,
        itemCoord.height
      );

      positionProperty.addSample(time, position);
    });

    entity.position = positionProperty;
  }

  console.log('%c [ entityArr ]-163', 'font-size:13px; background:#5af8ca; color:#9effff;', entityArr);

  // 设置时间轴和动画
  viewer.clock.startTime = startTime.clone();
  viewer.clock.stopTime = JulianDate.addSeconds(
    startTime,
    duration / 1000,
    new JulianDate()
  );
  viewer.clock.currentTime = startTime.clone();

  // 根据循环参数设置不同的时钟范围模式
  viewer.clock.clockRange = loop ? 2 : 1; // 2=LOOP_STOP(循环), 1=CLAMPED(单次)

  viewer.clock.multiplier = 1;
  viewer.timeline.zoomTo(startTime, viewer.clock.stopTime);

  // 开启动画
  viewer.clock.shouldAnimate = true;
  // viewer.trackedEntity = entity;
}

/**
* 三维地理坐标向量偏移函数
* @param {Object} pointA - 起点坐标 {longitude, latitude, height}
* @param {Object} pointB - 方向参考点坐标 {longitude, latitude, height}
* @param {number} distance - 移动距离(米)
* @returns {Object} 偏移后的坐标点 {longitude, latitude, height}
 */
function moveAlongVector(
  pointA: { longitude: number; latitude: number; height: number; },
  pointB: { longitude: number; latitude: number; height: number; },
  distance: number
) {
  // 转换为笛卡尔坐标系
  const cartesianA = Cartesian3.fromDegrees(
    pointA.longitude, pointA.latitude, pointA.height
  );
  const cartesianB = Cartesian3.fromDegrees(
    pointB.longitude, pointB.latitude, pointB.height
  );

  // 计算AB方向向量并归一化
  const direction = Cartesian3.subtract(
    cartesianA, cartesianB, new Cartesian3()  // 注意：这里交换了顺序，从B指向A
  );
  const normalizedDir = Cartesian3.normalize(
    direction, new Cartesian3()
  );

  // 计算偏移后的位置（直接在笛卡尔坐标系中进行）
  const offsetVector = Cartesian3.multiplyByScalar(
    normalizedDir, distance, new Cartesian3()
  );
  const resultPosition = Cartesian3.add(
    cartesianA, offsetVector, new Cartesian3()
  );

  // 转换回地理坐标
  const cartographic = Cartographic.fromCartesian(resultPosition);

  return {
    longitude: CesiumMath.toDegrees(cartographic.longitude),
    latitude: CesiumMath.toDegrees(cartographic.latitude),
    height: cartographic.height
  };
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
