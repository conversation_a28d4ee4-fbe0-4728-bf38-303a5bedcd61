/*
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-04-09
 * @Description:
 * @FilePath: \demo2\vite.config.ts
 */
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    hmr: {
      overlay: false,
    },
    proxy: {
      '/serverApi': {
        target: 'http://************:8005',
        changeOrigin: true,
        timeout: 3000000,
        // logLevel: 'debug',
        rewrite: (path) => path.replace(/^\/serverApi/, '')
      },
    }
  }
})
