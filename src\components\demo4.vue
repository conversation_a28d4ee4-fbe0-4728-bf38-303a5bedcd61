<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-04-01
 * @Description: 初始化cesium, 并添加了自带的3d建筑图层
 * @FilePath: \demo2\src\components\demo4.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import "cesium/Build/Cesium/Widgets/widgets.css";
import {
  Cartesian3,
  createOsmBuildingsAsync,
  Ion,
  Math as CesiumMath,
  Terrain,
  Viewer,
  Cesium3DTileset,
  IonResource,
  HeadingPitchRange,
  Matrix4
} from 'cesium';
import { onMounted, onBeforeUnmount } from 'vue';

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg';

let viewer: Viewer;

onMounted(() => {
  initCesiumMap();
})

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
})

async function initCesiumMap() {
  // Initialize the viewer with Cesium World Terrain.
  viewer = new Viewer('cesiumContainer', {
    // 禁用地形，防止地形与模型交互导致位置偏移
    terrain: undefined,
    baseLayerPicker: true,
    timeline: false,
    animation: false,
    homeButton: true
  });

  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;

  try {
    console.log('开始加载3D模型...');
    // 加载.b3dm文件的示例
    const tileset = await Cesium3DTileset.fromUrl('/static/tengfei/tileset.json', {
      maximumScreenSpaceError: 16, // 调整屏幕空间误差，值越大细节越低，性能越好
      maximumMemoryUsage: 2000, // 增大此值可提升渲染细节（减少瓦片频繁加载），但可能导致内存溢出；减小则降低内存压力，但可能增加加载卡顿
      maximumCacheOverflowBytes: 2000,
    });

    viewer.scene.globe.enableLighting = false; // 关闭全局光照，降低计算量
    // 关闭抗锯齿（FXAA）或阴影效果（如果启用）。
    viewer.scene.postProcessStages.fxaa.enabled = false;
    viewer.shadows = false;
    viewer.scene.debugShowFramesPerSecond = true; // 显示帧率

    console.log('3D模型加载成功，添加到场景...');
    // 添加tileset到场景
    viewer.scene.primitives.add(tileset);

    // 等待模型加载完成
    await tileset.readyPromise;
    console.log('模型准备完成，设置相机位置...');

    // 确保地球不会遮挡模型
    viewer.scene.globe.depthTestAgainstTerrain = false;

    // 修复模型位置偏移问题
    // 1. 禁用模型与地形的交互
    tileset.show = true;

    // 2. 设置正确的高度模式
    tileset.heightReference = undefined;

    // 3. 如果模型仍然偏移，可能需要手动调整模型位置
    // 这需要根据实际情况调整坐标值
    // tileset.modelMatrix = ...

    // 等待模型加载完成

    // 获取模型的中心位置
    const boundingSphere = tileset.boundingSphere;
    const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(boundingSphere.center);

    // 设置新的高度
    const newHeight = 25; // 设置新的绝对高度，单位为米
    cartographic.height = newHeight;

    // 计算新的中心位置
    const newCenter = viewer.scene.globe.ellipsoid.cartographicToCartesian(cartographic);

    // 计算偏移量
    const offset = Cartesian3.subtract(newCenter, boundingSphere.center, new Cartesian3());

    // 应用偏移量
    const translation = Matrix4.fromTranslation(offset);
    tileset.modelMatrix = translation;

    // 设置相机位置并飞行到tileset
    try {
      // 自动定位到模型
      await viewer.zoomTo(tileset);
      console.log('相机已定位到模型');

      // 保存当前视图作为home视图
      viewer.homeButton.viewModel.command.beforeExecute.addEventListener(function (e) {
        e.cancel = true;
        viewer.zoomTo(tileset);
      });
    } catch (error) {
      console.warn('自动定位到模型失败，使用默认位置:', error);
      // 使用手动设置的相机位置作为备选
      viewer.camera.flyTo({
        destination: Cartesian3.fromDegrees(116.4074, 39.9042, 5000) // 默认位置（北京）
      });
    }
  } catch (error) {
    console.error('加载3D Tiles失败:', error);
    alert('3D模型加载失败: ' + error.message);
  }
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
