<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-03-31
 * @Description: 加载谷歌的真实3d瓦片数据
 * @FilePath: \demo2\src\components\demo5.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import "cesium/Build/Cesium/Widgets/widgets.css";
import { Cartesian3, createOsmBuildingsAsync, Ion, Math as CesiumMath, Terrain, Viewer, IonGeocodeProviderType, createGooglePhotorealistic3DTileset } from 'cesium';
import { onMounted } from 'vue';

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg';

onMounted(() => {
  initCesiumMap();
})

/**
 * @description: 实例化cesium
 */
async function initCesiumMap() {
  // Initialize the Cesium Viewer in the HTML element with the `cesiumContainer` ID.
  const viewer = new Viewer('cesiumContainer', {
    // 关闭自带的地球，并指定谷歌编码器
    globe: false,
    geocoder: IonGeocodeProviderType.GOOGLE,
  });

  //使用谷歌地图的api创建一个3d瓦片
  try {
    const tileset = await createGooglePhotorealistic3DTileset();
    viewer.scene.primitives.add(tileset);
  } catch (error) {
    console.log(`Failed to load tileset: ${error}`);
  }

  // Fly the camera to San Francisco at the given longitude, latitude, and height.
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(-122.4175, 37.655, 400),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-15.0),
    }
  });

  // Add Cesium OSM Buildings, a global 3D buildings layer.
  // const buildingTileset = await createOsmBuildingsAsync();
  // viewer.scene.primitives.add(buildingTileset);
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
