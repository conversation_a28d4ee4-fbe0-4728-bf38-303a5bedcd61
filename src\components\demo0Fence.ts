import {
  Viewer,
  CustomDataSource,
  Event,
  Color,
  Property,
  Cartesian3,
  Material,
  defined,
} from 'cesium'

export function useFence(viewer: Viewer) {
  function map_common_addDatasouce(datasouceName: string) {
    let datasouce = viewer.dataSources._dataSources.find((t: { name: any }) => {
      return t && t.name == datasouceName
    })
    if (!datasouce) {
      datasouce = new CustomDataSource(datasouceName)
      viewer.dataSources.add(datasouce)
    }
    return datasouce
  }
  /*
    动态墙材质
    color 颜色
    duration 持续时间 毫秒
    trailImage 贴图地址
*/
  function DynamicWallMaterialProperty(options: { trailImage: any; color: any; duration: any }) {
    this._definitionChanged = new Event()
    this._color = undefined
    this._colorSubscription = undefined
    this.color = options.color || Color.BLUE
    this.duration = options.duration || 1000
    this.trailImage = options.trailImage
    this._time = new Date().getTime()
  }

  /**
   * 带方向的墙体
   * @param {*} options.get:true/false
   * @param {*} options.count:数量
   * @param {*} options.freely:vertical/standard
   * @param {*} options.direction:+/-
   */
  function _getDirectionWallShader(options: { get: any; count: any; freely: any; direction: any }) {
    if (options && options.get) {
      var materail =
        'czm_material czm_getMaterial(czm_materialInput materialInput)\n\
      {\n\
          czm_material material = czm_getDefaultMaterial(materialInput);\n\
          vec2 st = materialInput.st;'
      if (options.freely == 'vertical') {
        //（由下到上）
        materail +=
          'vec4 colorImage = texture(image, vec2(fract(st.s), fract(float(' +
          options.count +
          ')*st.t' +
          options.direction +
          ' time)));\n\ '
      } else {
        //（逆时针）
        materail +=
          'vec4 colorImage = texture(image, vec2(fract(float(' +
          options.count +
          ')*st.s ' +
          options.direction +
          ' time), fract(st.t)));\n\ '
      }
      //泛光
      materail +=
        'vec4 fragColor;\n\
          fragColor.rgb = (colorImage.rgb+color.rgb) / 1.0;\n\
          fragColor = czm_gammaCorrect(fragColor);\n\
          material.diffuse = colorImage.rgb;\n\
          material.alpha = colorImage.a;\n\
          material.emission = fragColor.rgb;\n\
          return material;\n\
      }'
      return materail
    }
  }

  Object.defineProperties(DynamicWallMaterialProperty.prototype, {
    isConstant: {
      get: function () {
        return false
      },
    },
    definitionChanged: {
      get: function () {
        return this._definitionChanged
      },
    },
    // color: Color.fromCssColorString('#7ffeff'),
  })

  var MaterialType = 'wallType' + parseInt(Math.random() * 1000)
  DynamicWallMaterialProperty.prototype.getType = function (time: any) {
    return MaterialType
  }

  DynamicWallMaterialProperty.prototype.getValue = function (time: any, result: { color?: any; image?: any; time?: any }) {
    if (!defined(result)) {
      result = {}
    }
    result.color = Property.getValueOrClonedDefault(
      this._color,
      time,
      Color.WHITE,
      result.color,
    )
    result.image = this.trailImage
    if (this.duration) {
      result.time = ((new Date().getTime() - this._time) % this.duration) / this.duration
    }
    viewer.scene.requestRender()
    return result
  }

  DynamicWallMaterialProperty.prototype.equals = function (other: { _color: any }) {
    return (
      this === other ||
      (other instanceof DynamicWallMaterialProperty &&
        Property.equals(this._color, other._color))
    )
  }

  Material._materialCache.addMaterial(MaterialType, {
    fabric: {
      type: MaterialType,
      uniforms: {
        color: new Color(1.0, 0.0, 0.0, 0.1),
        image: Material.DefaultImageId,
        time: -20,
      },
      source: _getDirectionWallShader({
        get: true,
        count: 3.0,
        freely: 'vertical',
        direction: '-',
      }),
    },
    translucent: function (material: any) {
      return true
    },
  })
  // DynamicWallMaterialProperty = DynamicWallMaterialProperty;

  //加载范围
  function loadWall() {
    let data = [
      [120.718823, 31.245826],
      [120.738823, 31.245826],
      [120.741823, 31.265826],
      [120.738823, 31.285826],
      [120.718823, 31.285826],
      [120.718823, 31.245826]
    ]
    let coor = Array.prototype.concat.apply([], data)
    let datasouce = map_common_addDatasouce('wall')

    datasouce.entities.add({
      wall: {
        positions: Cartesian3.fromDegreesArray(coor),
        maximumHeights: new Array(data.length).fill(500),
        minimunHeights: new Array(data.length).fill(0),

        // -----------静止-------
        // material: new ImageMaterialProperty({
        //   transparent: true,//设置透明
        //   image: "./fence-wall-2.png",
        //   repeat: new Cartesian2(1.0, 1),
        // }),

        // 动态
        material: new DynamicWallMaterialProperty({
          trailImage: '/static/image/wall.png',
          color: Color.CYAN,
          duration: 1500,
        }),
      },
    })
  }
  loadWall()
  //设置相机位置及方向
  // viewer.camera.flyTo({
  //   destination: Cartesian3.fromDegrees(104.02, 30.655, 5000),
  //   duration: 2,
  // })
}
