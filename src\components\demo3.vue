<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-04-11
 * @Description: 初始化cesium, 并添加了自带的3d建筑图层
 * @FilePath: \demo2\src\components\demo3.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import "cesium/Build/Cesium/Widgets/widgets.css";
import { Cartesian3, createOsmBuildingsAsync, Ion, Math as CesiumMath, Terrain, Viewer } from 'cesium';
import { onMounted } from 'vue';

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg';

onMounted(() => {
  initCesiumMap();
})

async function initCesiumMap() {
  // Keep your Cesium.Ion.defaultAccessToken = 'your_token_here' line above.
  // STEP 2 CODE
  // Initialize the viewer with Cesium World Terrain.
  const viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
  });

  // Fly the camera to Denver, Colorado at the given longitude, latitude, and height.
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(-104.9965, 39.74248, 4000)
  });

  // Add Cesium OSM Buildings.
  const buildingsTileset = await createOsmBuildingsAsync();
  viewer.scene.primitives.add(buildingsTileset);

  // STEP 3 CODE
  async function addBuildingGeoJSON() {
    // Load the GeoJSON file from Cesium ion.
    const geoJSONURL = await Cesium.IonResource.fromAssetId(your_asset_id);
    // Create the geometry from the GeoJSON, and clamp it to the ground.
    const geoJSON = await Cesium.GeoJsonDataSource.load(geoJSONURL, { clampToGround: true });
    // Add it to the scene.
    const dataSource = await viewer.dataSources.add(geoJSON);
    // By default, polygons in CesiumJS will be draped over all 3D content in the scene.
    // Modify the polygons so that this draping only applies to the terrain, not 3D buildings.
    for (const entity of dataSource.entities.values) {
      entity.polygon.classificationType = Cesium.ClassificationType.TERRAIN;
    }
    // Move the camera so that the polygon is in view.
    viewer.flyTo(dataSource);
  }
  addBuildingGeoJSON();
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
