<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-11-11
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-11
 * @Description: 3D流动管道组件
 * @FilePath: \demo2\src\components\FlowingPipe.vue
-->
<template>
  <div class="flowing-pipe-container">
    <div id="cesiumContainer" class="cesium-container"></div>
    <div class="control-panel">
      <h3>3D流动管道控制面板</h3>
      <div class="control-group">
        <label>管道半径:</label>
        <input type="range" min="1" max="10" step="0.5" v-model="pipeRadius" @input="updatePipe" />
        <span>{{ pipeRadius }}m</span>
      </div>
      <div class="control-group">
        <label>流动速度:</label>
        <input type="range" min="0.5" max="10" step="0.5" v-model="flowSpeed" @input="updatePipe" />
        <span>{{ flowSpeed }}</span>
      </div>
      <div class="control-group">
        <label>透明度:</label>
        <input type="range" min="0.1" max="1" step="0.1" v-model="opacity" @input="updatePipe" />
        <span>{{ opacity }}</span>
      </div>
      <div class="control-group">
        <label>管道颜色:</label>
        <select v-model="pipeColor" @change="updatePipe">
          <option value="#00BFFF">天蓝色</option>
          <option value="#00FF00">绿色</option>
          <option value="#FF0000">红色</option>
          <option value="#FFFF00">黄色</option>
          <option value="#FF00FF">紫色</option>
          <option value="#00FFFF">青色</option>
        </select>
      </div>
      <div class="control-group">
        <button @click="togglePipe" class="toggle-btn">
          {{ showPipe ? '隐藏管道' : '显示管道' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'cesium/Build/Cesium/Widgets/widgets.css'
import { Cartesian3, Ion, Terrain, Viewer, Math as CesiumMath } from 'cesium'
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { addPipeFlow } from './demo0index'

// Your access token can be found at: https://ion.cesium.com/tokens.
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg'

let viewer: Viewer
let currentPipe: any = null

// 响应式数据
const pipeRadius = ref(3)
const flowSpeed = ref(0.2)
const opacity = ref(0.7)
const pipeColor = ref('#00BFFF')
const showPipe = ref(true)

// 管道路径坐标（可以自定义）
const pipeCoordinates = [
  [120.727823, 31.266826, 10],
  [120.737823, 31.266826, 15],
  [120.737823, 31.276826, 20],
  [120.727823, 31.276826, 25],
  [120.720823, 31.276826, 30],
  [120.720823, 31.266826, 35],
]

onMounted(() => {
  initCesiumMap()
})

onBeforeUnmount(() => {
  if (viewer) viewer.destroy()
})

/**
 * @description: 初始化Cesium地图
 */
async function initCesiumMap() {
  // 初始化地图
  viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
    shouldAnimate: true,
    timeline: false,
    animation: false,
    fullscreenButton: false,
    vrButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    navigationHelpButton: false,
  })

  // 隐藏版权信息
  ;(viewer.cesiumWidget.creditContainer as HTMLDivElement).style.display = 'none'

  // 启用地形深度检测
  viewer.scene.globe.depthTestAgainstTerrain = true
  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false

  // 启用地球半透模式
  viewer.scene.globe.translucency.enabled = true
  viewer.scene.globe.translucency.frontFaceAlpha = 0.9

  viewer.scene.globe.enableLighting = false
  viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.shadows = false
  viewer.scene.debugShowFramesPerSecond = true

  // 1米内的剔除；防止模型闪烁
  viewer.camera.frustum.near = 1

  // 设置相机位置
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(120.732823, 31.271826, 200),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-45.0),
    },
  })

  // 创建初始管道
  createPipe()
}

/**
 * @description: 创建流动管道
 */
function createPipe() {
  if (currentPipe) {
    viewer.entities.remove(currentPipe)
    currentPipe = null
  }

  if (showPipe.value) {
    currentPipe = addPipeFlow(viewer, pipeCoordinates, {
      radius: pipeRadius.value,
      color: pipeColor.value,
      speed: flowSpeed.value,
      opacity: opacity.value,
    })
  }
}

/**
 * @description: 更新管道
 */
function updatePipe() {
  createPipe()
}

/**
 * @description: 切换管道显示/隐藏
 */
function togglePipe() {
  showPipe.value = !showPipe.value
  createPipe()
}
</script>

<style scoped>
.flowing-pipe-container {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 250px;
  font-family: Arial, sans-serif;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #00ffff;
  text-align: center;
}

.control-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-group label {
  font-size: 14px;
  margin-right: 10px;
  min-width: 80px;
}

.control-group input[type='range'] {
  flex: 1;
  margin: 0 10px;
}

.control-group select {
  flex: 1;
  padding: 5px;
  border-radius: 5px;
  border: none;
  background-color: #333;
  color: white;
}

.control-group span {
  min-width: 40px;
  text-align: right;
  font-size: 12px;
}

.toggle-btn {
  width: 100%;
  padding: 10px;
  background-color: #00ffff;
  color: black;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.toggle-btn:hover {
  background-color: #00cccc;
}
</style>
