/*
 * @Author: Strayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-12
 * @Description:
 * @FilePath: \demo2\src\router\index.ts
 */
import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/flowing-pipe',
      name: 'flowing-pipe',
      component: () => import('../components/FlowingPipe.vue'),
    },
    {
      path: '/simple-pipe',
      name: 'simple-pipe',
      component: () => import('../components/SimplePipe.vue'),
    },
    {
      path: '/level-based-pipe',
      name: 'level-based-pipe',
      component: () => import('../components/LevelBasedPipe.vue'),
    },
  ],
})

export default router
