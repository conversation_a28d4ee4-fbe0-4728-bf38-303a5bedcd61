{"name": "demo2", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.12.2"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"cesium": "^1.126.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/eslint-plugin": "1.1.31", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.20.1", "eslint-plugin-oxlint": "^0.15.10", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.15.10", "prettier": "^3.5.1", "typescript": "~5.7.3", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.5", "vue-tsc": "^2.2.2"}}