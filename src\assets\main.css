/* stylelint-disable selector-pseudo-element-no-unknown */
body,
ol,
ul,
h1,
h2,
h3,
h4,
h5,
h6,
p,
th,
td,
dl,
dd,
form,
fieldset,
legend,
input,
textarea,
select {
  margin: 0;
  padding: 0;
  outline: none;
}

body {
  color: #303133;
  background: var(--page-background);
  font:
    14px '微软雅黑',
    ta<PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    'Microsoft YaHei',
    'Hiragino Sans GB',
    sans-serif;
}

a {
  color: #666;
  text-decoration: none;
}

a:hover {
  color: #666;
  text-decoration: none;
  outline: none;
}

em {
  font-style: normal;
}

ul {
  list-style: none;
}

li {
  list-style: none;
}

img {
  border: 0;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

p {
  word-wrap: break-word;
}
