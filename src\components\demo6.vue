<!--
 * @Author: <PERSON>rayer
 * @Date: 2025-02-21
 * @LastEditors: Strayer
 * @LastEditTime: 2025-04-01
 * @Description: 相机相关
 * @FilePath: \demo2\src\components\demo6.vue
-->
<template>
  <div id="cesiumContainer"></div>
</template>

<script setup lang="ts">
import "cesium/Build/Cesium/Widgets/widgets.css";
import {
  Cartesian3, createOsmBuildingsAsync, Ion, Math as CesiumMath, Terrain, Viewer, ScreenSpaceEventHandler,
  ScreenSpaceEventType, Transforms, HeadingPitchRange
} from 'cesium';
import { onMounted } from 'vue';

// Your access token can be found at: https://ion.cesium.com/tokens.
// Replace `your_access_token` with your Cesium ion access token.
Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg';

onMounted(() => {
  initCesiumMap();
})

/**
 * @description: 实例化cesium
 */
async function initCesiumMap() {
  var viewer = new Viewer("cesiumContainer", {
    terrain: Terrain.fromWorldTerrain(),
  });

  // 飞到一个点
  // viewer.camera.flyTo({
  //   destination: Cesium.Cartesian3.fromDegrees(-117.16, 32.71, 15000.0),
  //   可选参数：朝向；俯仰角；翻滚角
  //   orientation: {
  //     heading: Cesium.Math.toRadians(20.0),
  //     pitch: Cesium.Math.toRadians(-35.0),
  //     roll: 0.0,
  //   },
  // });

  // 飞到一个实体要用viewer.flyTo
  // viewer.flyTo(rectangle);

  // 锁定相机到一个点
  var center = Cartesian3.fromRadians(2.4213211833389243, 0.6171926869414084, 3626.0426275055174);
  var transform = Transforms.eastNorthUpToFixedFrame(center);
  viewer.scene.camera.lookAtTransform(transform, new HeadingPitchRange(0, -Math.PI / 8, 2900));

  // 创建相机轨道：在每一个时钟tick中旋转相机
  viewer.clock.onTick.addEventListener(function (clock) {
    viewer.scene.camera.rotateRight(0.005);
  });

  // 禁用相机碰撞检测，使其可以进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;
}
</script>

<style scoped>
#cesiumContainer {
  width: 98vw;
  height: 90vh;
}
</style>
