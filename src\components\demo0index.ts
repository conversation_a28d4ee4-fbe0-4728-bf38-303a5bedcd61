import {
  Viewer,
  ScreenSpaceEventHandler,
  defined,
  Cartographic,
  ScreenSpaceEventType,
  Math as CesiumMath,
  Cesium3DTileset,
  Cartesian3,
  Matrix4,
  Cartesian2,
  Color,
  CornerType,
  Cesium3DTileStyle,
  ParticleSystem,
  ConeEmitter,
  ParticleBurst,
  CircleEmitter,
  HeadingPitchRoll,
  Quaternion,
  TranslationRotationScale,
  Transforms,
  Ellipsoid,
  PolylineGeometry,
  PolylineMaterialAppearance,
  GeometryInstance,
  Material,
  Primitive,
  MaterialProperty,
  Property,
  Event,
  JulianDate,
  PolylineGlowMaterialProperty,
  CustomShader,
  LightingModel,
  ImageMaterialProperty,
  HeightReference,
  CallbackProperty,
  ClassificationType,
  Plane,
  VerticalOrigin,
  CallbackPositionProperty
  // 移除 createPropertyDescriptor 导入
} from 'cesium'

/**
 * @description: 输出点击位置的坐标
 */
export function printPosition(viewer: Viewer) {
  var handler = new ScreenSpaceEventHandler(viewer.canvas)
  handler.setInputAction(function (event: any) {
    // 获取点击位置的笛卡尔坐标
    var pickedPosition = viewer.scene.pickPosition(event.position)

    if (defined(pickedPosition)) {
      // 将笛卡尔坐标转换为经纬度坐标
      var cartographic = Cartographic.fromCartesian(pickedPosition)
      var longitude = CesiumMath.toDegrees(cartographic.longitude) // 经度
      var latitude = CesiumMath.toDegrees(cartographic.latitude) // 纬度
      var height = cartographic.height // 高度，单位为米

      // 也可以输出原始笛卡尔坐标
      console.log('笛卡尔坐标：', pickedPosition)
      console.log(
        '%c [ cartographic ]-60',
        'font-size:13px; background:#0738f5; color:#4b7cff;',
        cartographic,
      )

      // 输出经纬度坐标
      console.log('经纬高：', longitude.toFixed(6), latitude.toFixed(6), height.toFixed(2))
    }
  }, ScreenSpaceEventType.LEFT_CLICK)
}

/**
 * @description: 开始加载3D模型
 */
export async function load3DTile(viewer: Viewer) {
  try {
    console.log('开始加载3D模型...')
    // 加载.b3dm文件的示例 /serverApi/static/3dtiles/gyz3dtiles/tileset.json    /static/tengfei/tileset.json
    const tileset = await Cesium3DTileset.fromUrl('/static/tengfei/tileset.json', {
      // skipLevelOfDetail: true,
      // baseScreenSpaceError: 1024,
      // skipScreenSpaceErrorFactor: 16,
      // skipLevels: 1,
      // immediatelyLoadDesiredLevelOfDetail: false,
      // loadSiblings: false,
      // cullWithChildrenBounds: true,

      // dynamicScreenSpaceError: true,
      // dynamicScreenSpaceErrorDensity: 2.0e-4,
      // dynamicScreenSpaceErrorFactor: 24.0, // 较大的值会导致加载较低分辨率的图块，从而略微提高运行时性能 降低视觉质量。该值必须为非负数
      // dynamicScreenSpaceErrorHeightFalloff: 0.25,

      // // 优化选项。移动时剔除请求中使用的乘数。较大的剔除较为激进，较小的剔除较不激进。默认60
      // cullRequestsWhileMovingMultiplier: 60,
      // // 以控制决定延迟哪些图块的圆锥体大小。 此圆锥体内的切片将立即加载。圆锥体外的图块可能会根据它们在圆锥体外的距离而延迟加载。
      // // 默认值为 0.3, 取值在 0~ 1 之间。值越小，性能越好，模糊区域越多
      // foveatedConeSize: 0.3,
      // maximumCacheOverflowBytes: 536870912, // 将用于缓存切片的最大额外 GPU 内存量
      // maximumScreenSpaceError: 24, // 用于驱动细节层次优化的最大屏幕空间误差。此值有助于确定何时显示 优化到其后代. 值越高，性能越好，但视觉质量越低
      // preloadWhenHidden: false, // 预加载图块。加载瓦片，就好像瓦片集可见一样，但不渲染它们

      // // 开发调试用
      // debugColorizeTiles: false, // 为每个平铺分配随机颜色。这对于可视化 哪些特征属于哪些切片，尤其是使用加法细化 Where 特征 From Parent 切片可以与 Child Tiles 中的要素交错。
      // debugShowMemoryUsage: false, // 绘制标签以指示每个图块的几何图形和纹理内存使用情况。
    })

    // 把模型弄为半透明
    // tileset.style = new Cesium3DTileStyle({
    //   color: 'color("rgba(255, 255, 255, 0.9)")'
    // });

    console.log('3D模型加载成功，添加到场景...')
    // 添加tileset到场景
    viewer.scene.primitives.add(tileset)

    // 等待模型加载完成
    // await tileset.readyPromise;
    console.log('模型准备完成，设置相机位置...')

    // 修复模型位置偏移问题
    // 1. 禁用模型与地形的交互
    tileset.show = true

    // 2. 设置正确的高度模式
    // // tileset.heightReference = undefined;
    // // 获取模型的中心位置
    // const boundingSphere = tileset.boundingSphere;
    // const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(boundingSphere.center);
    // // 设置新的高度
    // const newHeight = 36; // 设置新的绝对高度，单位为米
    // cartographic.height = newHeight;
    // // 计算新的中心位置
    // const newCenter = viewer.scene.globe.ellipsoid.cartographicToCartesian(cartographic);
    // // 计算偏移量
    // const offset = Cartesian3.subtract(newCenter, boundingSphere.center, new Cartesian3());
    // // 应用偏移量
    // const translation = Matrix4.fromTranslation(offset);
    // tileset.modelMatrix = translation;

    // 调整模型高度
    const heightOffset = 86.0
    const boundingSphere = tileset.boundingSphere
    const cartographic = Cartographic.fromCartesian(boundingSphere.center)
    const surface = Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0.0)
    const offset = Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      heightOffset,
    )
    const translation = Cartesian3.subtract(offset, surface, new Cartesian3())
    tileset.modelMatrix = Matrix4.fromTranslation(translation)

    return tileset
  } catch (error: any) {
    console.error('加载3D Tiles失败:', error)
    alert('3D模型加载失败: ' + error.message)
  }
}

/**
 * @description: 生成3d圆形管道
 * 实现流动管道思路: 1. 管道半透明. 2.弄一个亮色的球体+一连串圆环, 一直顺着管道的坐标移动
 */
export function addPipe(viewer: Viewer, width?: number) {
  // 1. 定义圆形截面生成函数（核心参数：半径）
  const computeCircle = (radius: number) => {
    const positions = []
    for (let i = 0; i < 360; i++) {
      const radians = CesiumMath.toRadians(i)
      positions.push(
        new Cartesian2(
          radius * Math.cos(radians), // X轴坐标
          radius * Math.sin(radians), // Y轴坐标
        ),
      )
    }
    return positions // 返回二维点数组描述圆形截面[1,2](@ref)
  }

  // 2. 创建管道实体
  const pipeline = viewer.entities.add({
    name: '3D圆形管道',
    polylineVolume: {
      positions: Cartesian3.fromDegreesArrayHeights([
        120.727823, 31.266826, 10,
        120.737823, 31.266826, 10,
        120.737823, 31.276826, 10,
        120.727823, 31.276826, 10,
      ]),
      shape: computeCircle(width ?? 2.0), // 截面半径（单位：米）
      material: Color.fromCssColorString('#a2e1fc').withAlpha(0.9), // 天蓝色材质
      cornerType: CornerType.ROUNDED, // 圆角过渡[1](@ref)
      granularity: CesiumMath.RADIANS_PER_DEGREE, // 控制路径插值精度[2](@ref)
    },
  })

  return pipeline
}

/**
 * @description: 生成3d圆形管道(带流动特效)
 */
export function addPipeFlow(viewer: Viewer, coordinates?: number[][], options?: {
  radius?: number,
  color?: string,
  speed?: number,
  opacity?: number
}) {
  // 默认配置
  const defaultOptions = {
    radius: 2.0,
    color: '#00BFFF',
    speed: 2,
    opacity: 0.7
  };

  const finalOptions = { ...defaultOptions, ...(options ?? {}) };

  // 默认坐标（如果没有提供）
  const defaultCoordinates = [
    [120.727823, 31.266826, 10],
    [120.737823, 31.266826, 10],
    [120.737823, 31.276826, 10],
    [120.727823, 31.276826, 10],
  ];

  const coords = coordinates || defaultCoordinates;

  // 1. 定义圆形截面生成函数（核心参数：半径）
  const computeCircle = (radius: number) => {
    const positions = []
    for (let i = 0; i < 360; i++) {
      const radians = CesiumMath.toRadians(i)
      positions.push(
        new Cartesian2(
          radius * Math.cos(radians), // X轴坐标
          radius * Math.sin(radians), // Y轴坐标
        ),
      )
    }
    return positions // 返回二维点数组描述圆形截面
  }

  // 创建自定义流动材质
  class FlowingMaterialProperty implements MaterialProperty {
    constructor(options: any = {}) {
      this._color = undefined;
      this._colorSubscription = undefined;
      this._speed = undefined;
      this._speedSubscription = undefined;
      this.color = options.color || Color.fromCssColorString(finalOptions.color).withAlpha(finalOptions.opacity);
      this.speed = options.speed || finalOptions.speed;
    }

    _definitionChanged = new Event();
    _color: any;
    _colorSubscription: any;
    _speed: any;
    _speedSubscription: any;
    color: any;
    speed: any;

    get isConstant() {
      return false;
    }

    get definitionChanged() {
      return this._definitionChanged;
    }

    getType() {
      return 'FlowLine';
    }

    getValue(time: any, result: any) {
      if (!result) {
        result = {};
      }
      result.color = this._color || Color.WHITE;
      result.speed = this._speed || finalOptions.speed;
      result.image = '/static/image/spriteline2.png';
      result.time = JulianDate.secondsDifference(time, JulianDate.now());
      return result;
    }

    equals(other: any) {
      return (
        this === other ||
        (other instanceof FlowingMaterialProperty &&
          this._color === other._color &&
          this._speed === other._speed)
      );
    }
  }

  // 替换 Object.defineProperties 的方式
  // 不使用 createPropertyDescriptor，直接定义 getter/setter
  Object.defineProperties(FlowingMaterialProperty.prototype, {
    color: {
      get: function () {
        return this._color;
      },
      set: function (value) {
        if (this._color !== value) {
          this._color = value;
          this._definitionChanged.raiseEvent(this);
        }
      }
    },
    speed: {
      get: function () {
        return this._speed;
      },
      set: function (value) {
        if (this._speed !== value) {
          this._speed = value;
          this._definitionChanged.raiseEvent(this);
        }
      }
    }
  });

  // 注册材质
  (Material as any).FlowLineType = 'FlowLine';
  (Material as any)._materialCache.addMaterial((Material as any).FlowLineType, {
    fabric: {
      type: (Material as any).FlowLineType,
      uniforms: {
        color: Color.fromCssColorString(finalOptions.color).withAlpha(finalOptions.opacity),
        speed: finalOptions.speed,
        image: '/static/image/spriteline2.png'
      },
      source: `
        czm_material czm_getMaterial(czm_materialInput materialInput) {
          czm_material material = czm_getDefaultMaterial(materialInput);
          vec2 st = materialInput.st;

          // 创建连续的流动效果，确保整个管道同时流动
          float time = czm_frameNumber * 0.01 * speed;
          float flowPattern = fract(st.s * 3.0 - time);

          // 使用纹理采样
          vec4 colorImage = texture(image, vec2(flowPattern, st.t));

          // 增强流动效果
          float pulse = sin(time * 2.0) * 0.3 + 0.7;

          material.alpha = colorImage.a * color.a * pulse;
          material.diffuse = color.rgb * colorImage.rgb;
          material.emission = color.rgb * colorImage.rgb * 0.5 * pulse;

          return material;
        }
      `
    },
    translucent: function (material: any) {
      return true;
    }
  });

  // 将坐标数组转换为Cartesian3数组
  const positions = [];
  for (let i = 0; i < coords.length; i++) {
    positions.push(coords[i][0], coords[i][1], coords[i][2]);
  }

  // 2. 创建管道实体
  const pipeline = viewer.entities.add({
    name: '3D圆形管道(流动特效)',
    polylineVolume: {
      positions: Cartesian3.fromDegreesArrayHeights(positions),
      shape: computeCircle(finalOptions.radius),
      cornerType: CornerType.ROUNDED,
      granularity: CesiumMath.RADIANS_PER_DEGREE,
      // 使用自定义流动材质
      material: new FlowingMaterialProperty({
        color: Color.fromCssColorString(finalOptions.color).withAlpha(finalOptions.opacity),
        speed: finalOptions.speed
      })
    }
  });

  return pipeline;
}




/**
 * @description: 添加发光折线（替代方法）
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} coordinates - 坐标数组，格式为 [lon1, lat1, height1, lon2, lat2, height2, ...]
 * @param {Object} options - 可选配置项
 * @returns {Entity} 返回创建的实体对象
 */
export function addGlowingPolylineEntity(viewer: Viewer, coordinates: number[][], options?: {
  width?: number, // 线宽
  glowPower?: number, // 发光强度，越小越亮
  color?: string, // 线条颜色
}) {
  // 默认配置
  const defaultOptions = {
    width: 20,                                    // 线宽
    glowPower: 0.1,                              // 发光强度，越小越亮
    color: '#00ffff'   // 线条颜色
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...(options ?? {}) };

  // 将坐标数组转换为Cartesian3数组
  const coordsCart: Cartesian3[] = [];
  for (let i = 0; i < coordinates.length; i++) {
    coordsCart.push(Cartesian3.fromDegrees(
      coordinates[i][0],
      coordinates[i][1],
      coordinates[i][2]
    ));
  }


  // 使用Entity API创建发光折线
  return viewer.entities.add({
    polyline: {
      positions: coordsCart,
      width: finalOptions.width,
      // 使用 PolylineGlowMaterialProperty 替代 Material
      material: new PolylineGlowMaterialProperty({
        color: Color.fromCssColorString(finalOptions.color),
        glowPower: finalOptions.glowPower
      })
    }
  });
}

/**
 * 创建流光折线
 * @param {Array} positions 坐标数组（经纬度高程）
 * @param {Color} color 发光颜色
 * @param {number} width 线宽
 * @param {number} speed 流光速度, 值越大，速度越快
 * @param {number} glowPower 发光强度, 值越大，发光越亮
 */
export function createGlowingPolyline(viewer: Viewer, coordinates: number[][], options?: {
  color?: string,
  width?: number,
  speed?: number,
  glowPower?: number
}) {
  // 坐标转换（支持三维坐标）
  const cartesians = coordinates.map(p =>
    Cartesian3.fromDegrees(p[0], p[1], p[2] || 0)
  );

  // 默认配置
  const defaultOptions = {
    width: 10,
    speed: 6.5,
    color: '#00ffff',
    glowPower: 6
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...(options ?? {}) };

  // 创建自定义MaterialProperty类
  class GlowLineMaterialProperty implements MaterialProperty {
    private _definitionChanged = new Event();
    private _color: Color;
    private _glowPower: number;
    private _speed: number;
    private _time: number;
    private _startTime: JulianDate;

    constructor(color: Color, speed: number, glowPower: number) {
      this._color = color;
      this._speed = speed;
      this._glowPower = glowPower;
      this._time = 0.0;
      this._startTime = JulianDate.now(); // 记录开始时间
    }

    getType(): string {
      return 'WgFlowLight';
    }

    getValue(time: JulianDate, result?: any): any {
      if (!result) {
        result = {};
      }

      // 计算自开始以来的时间差（秒）
      // 使用 JulianDate.secondsDifference 而不是 toSeconds
      const currentTime = JulianDate.secondsDifference(time, this._startTime);
      this._time = currentTime;

      result.u_color = this._color;
      result.u_speed = this._speed;
      result.u_time = this._time;
      result.u_glowPower = this._glowPower;

      return result;
    }

    equals(other?: MaterialProperty): boolean {
      return (
        other instanceof GlowLineMaterialProperty &&
        this._color.equals((<GlowLineMaterialProperty>other)._color) &&
        this._speed === (<GlowLineMaterialProperty>other)._speed &&
        this._glowPower === (<GlowLineMaterialProperty>other)._glowPower
      );
    }

    get definitionChanged() {
      return this._definitionChanged;
    }

    get isConstant() {
      return false;
    }
  }

  // 注册材质
  (Material as any).WgFlowLightType = 'WgFlowLight';
  (Material as any)._materialCache.addMaterial((Material as any).WgFlowLightType, {
    fabric: {
      type: (Material as any).WgFlowLightType,
      uniforms: {
        u_color: Color.fromCssColorString(finalOptions.color),
        u_time: 0.0,
        u_speed: finalOptions.speed,
        u_glowPower: finalOptions.glowPower,
      },
      source: `
        czm_material czm_getMaterial(czm_materialInput materialInput) {
          czm_material material = czm_getDefaultMaterial(materialInput);

          // 计算流动光效（基于纹理坐标）
          float progress = fract(u_time * u_speed * 0.1 + materialInput.st.s);
          float glow = smoothstep(0.3, 0.7, abs(progress - 0.5));

          // 设置自发光颜色（emission属性决定发光强度）
          material.emission = u_color.rgb * glow * u_glowPower;
          material.alpha = u_color.a * glow;
          return material;
        }
      `
    },
    translucent: function () {
      return true;
    }
  });

  // 创建折线实体，使用自定义MaterialProperty
  const entity = viewer.entities.add({
    polyline: {
      positions: cartesians,
      width: finalOptions.width,
      material: new GlowLineMaterialProperty(Color.fromCssColorString(finalOptions.color), finalOptions.speed, finalOptions.glowPower)
    }
  });

  return entity;
}

/**
 * @description: 创建带发光边缘的地面多边形
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} coordinates - 坐标数组，格式为 [lon1, lat1, lon2, lat2, ...]（不需要高度）
 * @param {Object} options - 可选配置项
 * @returns {Entity[]} 返回创建的实体对象数组
 */
export function addGlowingPolygon(viewer: Viewer, coordinates: number[], options: any = {}) {
  // 默认配置
  const defaultOptions = {
    polygonColor: Color.fromCssColorString('#00ffff').withAlpha(0.5), // 多边形填充颜色
    outlineColor: Color.fromCssColorString('#80FFFF'),                // 边缘线颜色
    glowColor: Color.fromCssColorString('#afFFFF'),                   // 发光颜色
    outlineWidth: 2,                                                  // 边缘线宽度
    glowWidth: 80,                                                    // 发光宽度
    glowPower: 0.1,                                                  // 发光强度（越小越亮）
    height: 0                                                       // 高度偏移（米）
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...options };
  const entities: any[] = [];

  // 创建多边形坐标数组（无高度）
  const polygonPositions = [];
  for (let i = 0; i < coordinates.length; i += 2) {
    polygonPositions.push(coordinates[i], coordinates[i + 1]);
  }

  // 创建边缘线坐标数组（带高度）
  const outlinePositions = [];
  for (let i = 0; i < coordinates.length; i += 2) {
    outlinePositions.push(
      coordinates[i],
      coordinates[i + 1],
      finalOptions.height
    );
  }
  // 闭合轮廓
  outlinePositions.push(
    coordinates[0],
    coordinates[1],
    finalOptions.height
  );

  // 1. 添加基础多边形（填充区域）
  const polygon = viewer.entities.add({
    polygon: {
      hierarchy: Cartesian3.fromDegreesArray(polygonPositions),
      material: finalOptions.polygonColor,
      outline: true,
      outlineColor: finalOptions.outlineColor,
      outlineWidth: finalOptions.outlineWidth,
      height: 0,
      zIndex: 0
    }
  });
  entities.push(polygon);

  // 2. 添加发光边缘线（使用PolylineGlowMaterialProperty）
  const glowingOutline = viewer.entities.add({
    polyline: {
      positions: Cartesian3.fromDegreesArrayHeights(outlinePositions),
      width: finalOptions.glowWidth,
      material: new PolylineGlowMaterialProperty({
        color: finalOptions.glowColor,
        glowPower: finalOptions.glowPower
      }),
      clampToGround: false,
      zIndex: 1
    }
  });
  entities.push(glowingOutline);

  return entities;
}

/**
 * @description: 创建地面旋转图标
 * @param {Viewer} viewer - Cesium Viewer实例
 * @param {Array} position - 位置坐标，格式为 [lon, lat, height(可选)]
 * @param {Object} options - 可选配置项
 * @returns {Object} 返回创建的实体对象和控制函数
 */
export function addRotatingGroundImage(viewer: Viewer) {
  let rotation = CesiumMath.toRadians(180);

  function getRotationValue() {
    rotation += 0.115;
    return rotation;
  }

  // 使用ImageMaterialProperty来正确处理透明度
  const imageMaterial = new ImageMaterialProperty({
    image: "/static/scan_blue.png",
    transparent: true,  // 启用透明度
    color: Color.WHITE.withAlpha(1.0)  // 保持图片原色，但可以调整整体透明度
  });

  // 定义位置和高度
  const longitude = 120.729823;
  const latitude = 31.256826;
  const height = 170;
  const baseHeight = height + 10;

  // 添加椭圆
  const ellipse = viewer.entities.add({
    position: Cartesian3.fromDegrees(longitude, latitude, 0),
    name: "监测点A",
    ellipse: {
      material: imageMaterial,
      semiMinorAxis: 20.0,
      semiMajorAxis: 20.0,
      height: height,
      outline: true,
      rotation: new CallbackProperty(getRotationValue, false),
      stRotation: new CallbackProperty(getRotationValue, false),
      classificationType: ClassificationType.TERRAIN,
    }
  });

  // 添加棱镜图标
  const prism = viewer.entities.add({
    position: Cartesian3.fromDegrees(longitude, latitude, baseHeight),
    name: "监测点A",
    billboard: {
      image: "/static/prism_blue.png",
      width: 20,
      height: 20,
      sizeInMeters: true,
    },
  });

  // 该平面不会始终面相用户，会随地图旋转
  // const skyPlane = viewer.entities.add({
  //   name: "垂直于地面的平面",
  //   position: Cartesian3.fromDegrees(120.729823, 31.256826, 50),
  //   plane: {
  //     plane: new Plane(Cartesian3.UNIT_Y, 0), // Y轴作为法线，使平面垂直于地面
  //     dimensions: new Cartesian2(100, 100),
  //     material: imageMaterial
  //   }
  // });

  // 为triangle添加上下浮动效果
  const floatAmplitude = 5; // 浮动幅度（米）
  const floatPeriod = 2; // 浮动周期（秒）
  const startTime = JulianDate.now(); // 记录开始时间

  // 创建位置回调函数，实现上下浮动
  const getFloatingPosition = new CallbackPositionProperty((time) => {
    // 计算自开始以来的时间（秒）
    const seconds = JulianDate.secondsDifference(time!, startTime);
    // 使用正弦函数计算当前高度偏移
    const heightOffset = floatAmplitude * Math.sin((seconds * Math.PI * 2) / floatPeriod);
    // 返回新的位置
    return Cartesian3.fromDegrees(longitude, latitude, baseHeight + heightOffset);
  }, false);

  // 添加三角形图标
  const triangle = viewer.entities.add({
    position: getFloatingPosition,
    name: "监测点A",
    billboard: {
      image: "/static/triangle_blue.png",
      width: 20,
      height: 20,
      sizeInMeters: true,
    },
  });

  // 创建HTML覆盖层
  createInfoOverlay(viewer, longitude, latitude, baseHeight + 15, {
    name: "监测点A",
    pressure: "1.5 MPa",
    flow: "25 m³/h",
    temperature: "28°C"
  });

  return { ellipse, prism, triangle };
}

/**
 * 创建信息覆盖层
 * @param viewer Cesium Viewer实例
 * @param longitude 经度
 * @param latitude 纬度
 * @param height 高度
 * @param info 要显示的信息对象
 */
function createInfoOverlay(viewer: Viewer, longitude: number, latitude: number, height: number, info: any) {
  // 创建HTML元素
  const overlayDiv = document.createElement('div');
  overlayDiv.className = 'cesium-info-overlay';
  overlayDiv.innerHTML = `
    <div class="cesium-info-card">
      <div class="cesium-info-title">${info.name}</div>
      <div class="cesium-info-content">
        <div class="cesium-info-item">压力: ${info.pressure}</div>
        <div class="cesium-info-item">流量: ${info.flow}</div>
        <div class="cesium-info-item">温度: ${info.temperature}</div>
      </div>
    </div>
  `;

  // 设置样式
  const style = document.createElement('style');
  style.textContent = `
    .cesium-info-overlay {
      position: absolute;
      pointer-events: none;
      z-index: 1;
      transform: translate(-50%, -100%);
    }
    .cesium-info-card {
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 5px;
      color: white;
      padding: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
      font-family: Arial, sans-serif;
      min-width: 150px;
    }
    .cesium-info-title {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 5px;
      color: #00ffff;
      text-align: center;
    }
    .cesium-info-content {
      font-size: 12px;
    }
    .cesium-info-item {
      margin: 3px 0;
    }
  `;
  document.head.appendChild(style);

  // 添加到DOM
  viewer.container.appendChild(overlayDiv);

  // 更新位置函数
  function updatePosition() {
    const position = Cartesian3.fromDegrees(longitude, latitude, height);
    const canvasPosition = viewer.scene.cartesianToCanvasCoordinates(position);

    if (defined(canvasPosition)) {
      overlayDiv.style.left = canvasPosition.x + 'px';
      overlayDiv.style.top = canvasPosition.y + 'px';
      overlayDiv.style.display = 'block';
    } else {
      overlayDiv.style.display = 'none';
    }
  }

  // 初始更新位置
  updatePosition();

  // 添加事件监听器，在相机移动时更新位置
  viewer.scene.postRender.addEventListener(updatePosition);

  // 返回清理函数
  return function cleanup() {
    viewer.container.removeChild(overlayDiv);
    viewer.scene.postRender.removeEventListener(updatePosition);
    document.head.removeChild(style);
  };
}
