{"asset": {"generatetool": "<EMAIL>", "gltfUpAxis": "Z", "version": "1.0"}, "geometricError": 3.25042927265167, "root": {"boundingVolume": {"box": [-202.668053078873, 53.2380246689235, -2.61566818116575, 51.1618385314941, 0, 0, 0, 51.952657699585, 0, 0, 0, 36.8016242980957]}, "children": [{"boundingVolume": {"box": [-219.486668116229, 22.9241689614564, -18.1432705959656, 27.9757471084595, 0, 0, 0, 21.6388025283813, 0, 0, 0, 5.94853615760803]}, "children": [{"boundingVolume": {"box": [-231.156335400909, 22.9240337584528, -17.9973606574178, 16.3059587478638, 0, 0, 0, 21.6386661529541, 0, 0, 0, 5.8123722076416]}, "children": [{"boundingVolume": {"box": [-224.862732051855, 13.9079428323083, -17.9944271248418, 10.0071320533752, 0, 0, 0, 12.6225748062134, 0, 0, 0, 5.81941723823547]}, "content": {"uri": "BlockXB_L21_82.b3dm"}, "geometricError": 0.788908720016479}, {"boundingVolume": {"box": [-237.966525692575, 35.5464283101847, -19.3243334939162, 9.49576759338379, 0, 0, 0, 9.01618051528931, 0, 0, 0, 4.23900246620178]}, "content": {"uri": "BlockXB_L21_81.b3dm"}, "geometricError": 0.593477249145508}, {"boundingVolume": {"box": [-221.664733298105, 35.5465103281813, -19.1879466283052, 6.80900859832764, 0, 0, 0, 9.01619005203247, 0, 0, 0, 4.53856039047241]}, "children": [{"boundingVolume": {"box": [-221.664458765748, 30.2870256478663, -18.8082260271428, 6.80827617645264, 0, 0, 0, 3.75915265083313, 0, 0, 0, 4.91345238685608]}, "content": {"uri": "BlockXB_L22_227.b3dm"}, "geometricError": 0.425514221191406}, {"boundingVolume": {"box": [-221.664527255571, 39.3032219356635, -19.1140660701946, 6.80817723274231, 0, 0, 0, 5.25947880744934, 0, 0, 0, 4.47325563430786]}, "content": {"uri": "BlockXB_L22_226.b3dm"}, "geometricError": 0.425507545471191}], "content": {"uri": "BlockXB_L21_80.b3dm"}, "geometricError": 0.563506364822388}], "content": {"uri": "BlockXB_L20_31.b3dm"}, "geometricError": 1.35241508483887}, {"boundingVolume": {"box": [-203.182066962216, 22.9241827361759, -17.8245696277656, 11.677098274231, 0, 0, 0, 21.6387872695923, 0, 0, 0, 6.27085089683533]}, "children": [{"boundingVolume": {"box": [-203.183646918563, 13.9080766552785, -19.4816368669466, 11.6727285385132, 0, 0, 0, 12.622682094574, 0, 0, 0, 4.60087394714355]}, "children": [{"boundingVolume": {"box": [-208.048832225942, 8.64862306010299, -23.3921231987001, 6.80742502212524, 0, 0, 0, 7.36322832107544, 0, 0, 0, 0.632829517126083]}, "content": {"uri": "BlockXB_L22_225.b3dm"}, "geometricError": 0.460196733474731}, {"boundingVolume": {"box": [-196.377955459435, 8.64876101773643, -21.4285764182121, 4.86386299133301, 0, 0, 0, 7.36321067810059, 0, 0, 0, 2.64636409282684]}, "content": {"uri": "BlockXB_L22_224.b3dm"}, "geometricError": 0.460196733474731}, {"boundingVolume": {"box": [-209.019156484835, 21.2711871900478, -20.7274106184776, 5.83721876144409, 0, 0, 0, 5.25949382781982, 0, 0, 0, 3.12856531143188]}, "content": {"uri": "BlockXB_L22_223.b3dm"}, "geometricError": 0.364822387695313}, {"boundingVolume": {"box": [-197.352034919498, 21.2712931290637, -19.287819797073, 5.83483505249023, 0, 0, 0, 5.25946545600891, 0, 0, 0, 4.46326208114624]}, "content": {"uri": "BlockXB_L22_222.b3dm"}, "geometricError": 0.364677429199219}], "content": {"uri": "BlockXB_L21_79.b3dm"}, "geometricError": 0.788908720016479}, {"boundingVolume": {"box": [-209.996093693134, 35.5466462331003, -19.0797424026735, 4.86435294151306, 0, 0, 0, 9.01615571975708, 0, 0, 0, 4.44711422920227]}, "children": [{"boundingVolume": {"box": [-212.426163977399, 31.0386175530365, -19.3234292012699, 2.43272173404694, 0, 0, 0, 4.50812649726868, 0, 0, 0, 4.19236826896667]}, "content": {"uri": "BlockXB_L22_221.b3dm"}, "geometricError": 0.281753301620483}, {"boundingVolume": {"box": [-207.561461675858, 31.0386812319932, -19.1652220731074, 2.4341344833374, 0, 0, 0, 4.508145570755, 0, 0, 0, 4.34970617294312]}, "content": {"uri": "BlockXB_L22_220.b3dm"}, "geometricError": 0.281753301620483}, {"boundingVolume": {"box": [-209.994562872202, 40.0546980755694, -18.9753955756759, 4.86270976066589, 0, 0, 0, 4.50810384750366, 0, 0, 0, 4.33877038955688]}, "content": {"uri": "BlockXB_L22_219.b3dm"}, "geometricError": 0.303919792175293}], "content": {"uri": "BlockXB_L21_78.b3dm"}, "geometricError": 0.563506364822388}, {"boundingVolume": {"box": [-198.32468581498, 35.5467833946379, -17.42926351388, 6.80781173706055, 0, 0, 0, 9.01618671417236, 0, 0, 0, 5.89928126335144]}, "children": [{"boundingVolume": {"box": [-198.324578867642, 30.2873958651163, -19.7753454518255, 6.80770492553711, 0, 0, 0, 3.75679898262024, 0, 0, 0, 3.53978598117828]}, "content": {"uri": "BlockXB_L22_218.b3dm"}, "geometricError": 0.425483703613281}, {"boundingVolume": {"box": [-198.324439904914, 39.3034974942199, -16.9208352961324, 6.80756592750549, 0, 0, 0, 5.25947260856628, 0, 0, 0, 6.39351367950439]}, "content": {"uri": "BlockXB_L22_217.b3dm"}, "geometricError": 0.425468444824219}], "content": {"uri": "BlockXB_L21_77.b3dm"}, "geometricError": 0.563506364822388}], "content": {"uri": "BlockXB_L20_30.b3dm"}, "geometricError": 1.35241508483887}], "content": {"uri": "BlockXB_L19_15.b3dm"}, "geometricError": 1.74846839904785}, {"boundingVolume": {"box": [-171.513912789696, 22.9024663523936, 5.28429239466533, 20.0234775543213, 0, 0, 0, 21.6609754562378, 0, 0, 0, 28.9044322967529]}, "children": [{"boundingVolume": {"box": [-171.509214419562, 22.9182170900633, -9.13937444871976, 20.0223188400269, 0, 0, 0, 21.6452255249023, 0, 0, 0, 14.4505848884583]}, "children": [{"boundingVolume": {"box": [-178.17684262641, 12.1051668295254, -19.7210866819383, 13.3404688835144, 0, 0, 0, 10.8194842338562, 0, 0, 0, 3.84183967113495]}, "children": [{"boundingVolume": {"box": [-184.847947517661, 12.1050852765093, -19.7203115627167, 6.66936349868774, 0, 0, 0, 10.8194026947021, 0, 0, 0, 3.84012579917908]}, "content": {"uri": "BlockXB_L22_216.b3dm"}, "geometricError": 0.676207542419434}, {"boundingVolume": {"box": [-171.510409062516, 12.1052438474417, -20.3757916621837, 6.67031240463257, 0, 0, 0, 10.8194069862366, 0, 0, 0, 2.72607684135437]}, "content": {"uri": "BlockXB_L22_215.b3dm"}, "geometricError": 0.676207542419434}], "content": {"uri": "BlockXB_L21_76.b3dm"}, "geometricError": 0.833769798278809}, {"boundingVolume": {"box": [-158.171455570944, 12.0993570322433, -8.79224156237269, 6.67265391349792, 0, 0, 0, 10.8254418373108, 0, 0, 0, 14.1034517288208]}, "children": [{"boundingVolume": {"box": [-158.173769939436, 4.8905014605149, -15.7860390085574, 6.67017650604248, 0, 0, 0, 3.60877931118011, 0, 0, 0, 6.99348521232605]}, "content": {"uri": "BlockXB_L22_214.b3dm"}, "geometricError": 0.416879653930664}, {"boundingVolume": {"box": [-158.173607253251, 15.7100601743136, -15.8413906881452, 6.67050194740295, 0, 0, 0, 7.21473908424377, 0, 0, 0, 7.04849314689636]}, "content": {"uri": "BlockXB_L22_213.b3dm"}, "geometricError": 0.450928688049316}, {"boundingVolume": {"box": [-157.817964615821, 5.12163032252506, -4.67913785716917, 6.31303906440735, 0, 0, 0, 3.83848392963409, 0, 0, 0, 4.11377692222595]}, "content": {"uri": "BlockXB_L22_212.b3dm"}, "geometricError": 0.394564628601074}, {"boundingVolume": {"box": [-157.795913388012, 5.07897784774023, 2.37274820295993, 6.29131817817688, 0, 0, 0, 3.79521024227142, 0, 0, 0, 2.93846237659454]}, "content": {"uri": "BlockXB_L22_211.b3dm"}, "geometricError": 0.393208503723145}], "content": {"uri": "BlockXB_L21_75.b3dm"}, "geometricError": 0.676593542098999}, {"boundingVolume": {"box": [-171.516619999385, 33.7438809581862, -19.8604413184973, 20.0092678070068, 0, 0, 0, 10.8195614814758, 0, 0, 0, 3.35998320579529]}, "children": [{"boundingVolume": {"box": [-181.514655944115, 28.3341066698649, -22.3807456531586, 10.002760887146, 0, 0, 0, 5.40978670120239, 0, 0, 0, 0.829119890928268]}, "content": {"uri": "BlockXB_L22_210.b3dm"}, "geometricError": 0.62517261505127}, {"boundingVolume": {"box": [-181.51744140832, 39.1534367391112, -22.2934984339152, 10.0055470466614, 0, 0, 0, 5.40977644920349, 0, 0, 0, 0.819902241230011]}, "content": {"uri": "BlockXB_L22_209.b3dm"}, "geometricError": 0.62534236907959}, {"boundingVolume": {"box": [-161.509989796181, 28.3343395046144, -19.7741577578386, 10.0025463104248, 0, 0, 0, 5.40978050231934, 0, 0, 0, 3.27771937847137]}, "content": {"uri": "BlockXB_L22_208.b3dm"}, "geometricError": 0.625156402587891}, {"boundingVolume": {"box": [-161.510149756166, 39.1536678093647, -19.7340995588155, 10.0025191307068, 0, 0, 0, 5.40977454185486, 0, 0, 0, 3.17569649219513]}, "content": {"uri": "BlockXB_L22_207.b3dm"}, "geometricError": 0.625155448913574}], "content": {"uri": "BlockXB_L21_74.b3dm"}, "geometricError": 1.25057411193848}], "content": {"uri": "BlockXB_L20_29.b3dm"}, "geometricError": 1.35283780097961}, {"boundingVolume": {"box": [-157.750173104076, 5.05072983853309, 19.7495167764779, 6.24728679656982, 0, 0, 0, 3.76827108860016, 0, 0, 0, 14.4386587142944]}, "children": [{"boundingVolume": {"box": [-157.750173135117, 5.05072985492649, 10.9262352421855, 6.2472870349884, 0, 0, 0, 3.76827120780945, 0, 0, 0, 5.6153769493103]}, "children": [{"boundingVolume": {"box": [-160.916540906222, 3.15002010854148, 10.9263295349922, 3.08302485942841, 0, 0, 0, 1.86578387022018, 0, 0, 0, 5.61528277397156]}, "content": {"uri": "BlockXB_L23_797.b3dm"}, "geometricError": 0.19268798828125}, {"boundingVolume": {"box": [-154.670610747913, 5.38952229019198, 10.9261792540466, 3.16571033000946, 0, 0, 0, 3.43031787872314, 0, 0, 0, 5.61532115936279]}, "content": {"uri": "BlockXB_L23_796.b3dm"}, "geometricError": 0.214397192001343}], "content": {"uri": "BlockXB_L22_326.b3dm"}, "geometricError": 0.390459060668945}, {"boundingVolume": {"box": [-157.700807121856, 5.00985050327988, 20.5522947929279, 6.19676852226257, 0, 0, 0, 3.72506749629974, 0, 0, 0, 4.01103615760803]}, "children": [{"boundingVolume": {"box": [-160.828547688783, 3.13485489266844, 20.5523883582047, 3.06939435005188, 0, 0, 0, 1.84855359792709, 0, 0, 0, 4.01094245910645]}, "content": {"uri": "BlockXB_L23_795.b3dm"}, "geometricError": 0.191835403442383}, {"boundingVolume": {"box": [-154.633482104488, 5.37394067663655, 20.5522168906972, 3.12822282314301, 0, 0, 0, 3.36074101924896, 0, 0, 0, 4.01095819473267]}, "content": {"uri": "BlockXB_L23_794.b3dm"}, "geometricError": 0.210042238235474}], "content": {"uri": "BlockXB_L22_325.b3dm"}, "geometricError": 0.387297630310059}, {"boundingVolume": {"box": [-160.298109693142, 3.46859281067118, 29.3585335773991, 3.59162330627441, 0, 0, 0, 2.18450081348419, 0, 0, 0, 4.79539012908936]}, "children": [{"boundingVolume": {"box": [-160.289476153748, 3.43010430575427, 26.9621051513701, 3.5819673538208, 0, 0, 0, 2.14498937129974, 0, 0, 0, 2.39896166324615]}, "content": {"uri": "BlockXB_L23_793.b3dm"}, "geometricError": 0.223875045776367}, {"boundingVolume": {"box": [-160.297581111817, 3.46962702121415, 31.7597452870275, 3.59032070636749, 0, 0, 0, 2.18428301811218, 0, 0, 0, 2.3988687992096]}, "content": {"uri": "BlockXB_L24_825.b3dm"}, "geometricError": 0.224397659301758}], "content": {"uri": "BlockXB_L22_324.b3dm"}, "geometricError": 0.224480628967285}, {"boundingVolume": {"box": [-154.10642263436, 5.03596819273723, 29.3755824680545, 2.60180413722992, 0, 0, 0, 3.74888670444489, 0, 0, 0, 4.81259322166443]}, "children": [{"boundingVolume": {"box": [-154.106804193447, 6.56878216898583, 27.370670232369, 2.60142278671265, 0, 0, 0, 2.13763236999512, 0, 0, 0, 2.8076810836792]}, "content": {"uri": "BlockXB_L23_792.b3dm"}, "geometricError": 0.162590026855469}, {"boundingVolume": {"box": [-154.107165065849, 3.51494905780115, 32.1671392727715, 2.60095202922821, 0, 0, 0, 2.22786748409271, 0, 0, 0, 1.98882466554642]}, "content": {"uri": "BlockXB_L24_824.b3dm"}, "geometricError": 0.162557601928711}, {"boundingVolume": {"box": [-154.032521206523, 7.26202667999043, 32.1836205424064, 2.52666747570038, 0, 0, 0, 1.52025669813156, 0, 0, 0, 2.00542891025543]}, "content": {"uri": "BlockXB_L24_823.b3dm"}, "geometricError": 0.157919883728027}], "content": {"uri": "BlockXB_L23_881.b3dm"}, "geometricError": 0.234302759170532}], "content": {"uri": "BlockXB_L21_119.b3dm"}, "geometricError": 0.390154838562012}], "content": {"uri": "BlockXB_L19_14.b3dm"}, "geometricError": 1.35382223129272}, {"boundingVolume": {"box": [-223.985231371905, 66.7370155404357, -24.6943492495892, 29.8446283340454, 0, 0, 0, 22.1747055053711, 0, 0, 0, 14.2399930953979]}, "children": [{"boundingVolume": {"box": [-236.420897795361, 62.0021680455801, -24.7229239071196, 17.4090232849121, 0, 0, 0, 17.4398593902588, 0, 0, 0, 14.2722673416138]}, "children": [{"boundingVolume": {"box": [-236.420793566938, 51.8351115886115, -24.4626873989821, 17.4090967178345, 0, 0, 0, 7.27280211448669, 0, 0, 0, 12.778151512146]}, "children": [{"boundingVolume": {"box": [-243.674488324976, 51.8350880137743, -17.5388626591654, 10.1554017066956, 0, 0, 0, 7.27277851104736, 0, 0, 0, 5.84501361846924]}, "content": {"uri": "BlockXB_L22_206.b3dm"}, "geometricError": 0.634709358215332}, {"boundingVolume": {"box": [-225.44831463117, 54.2937992568839, -30.3586927213903, 6.43664860725403, 0, 0, 0, 4.8141143321991, 0, 0, 0, 6.87946128845215]}, "content": {"uri": "BlockXB_L22_205.b3dm"}, "geometricError": 0.402284622192383}, {"boundingVolume": {"box": [-226.267215553525, 51.8351861383386, -18.5655501864077, 7.25551915168762, 0, 0, 0, 7.27271366119385, 0, 0, 0, 4.91382527351379]}, "content": {"uri": "BlockXB_L22_204.b3dm"}, "geometricError": 0.454543352127075}], "content": {"uri": "BlockXB_L21_73.b3dm"}, "geometricError": 1.08806800842285}, {"boundingVolume": {"box": [-236.238769808672, 69.2683867985262, -24.7038621593309, 17.226713180542, 0, 0, 0, 10.1665887832642, 0, 0, 0, 14.2587814331055]}, "children": [{"boundingVolume": {"box": [-244.851734332784, 65.9943436713169, -24.7112554294685, 8.61377811431885, 0, 0, 0, 6.88671493530273, 0, 0, 0, 14.2638711929321]}, "content": {"uri": "BlockXB_L22_203.b3dm"}, "geometricError": 0.538360595703125}, {"boundingVolume": {"box": [-227.626661719375, 69.2684972304778, -31.9424609588379, 8.6175684928894, 0, 0, 0, 10.1638193130493, 0, 0, 0, 6.68236923217773]}, "content": {"uri": "BlockXB_L22_202.b3dm"}, "geometricError": 0.635237455368042}, {"boundingVolume": {"box": [-227.628021903895, 69.2683527429137, -18.6282028311984, 8.61596441268921, 0, 0, 0, 10.1634683609009, 0, 0, 0, 6.63246631622314]}, "content": {"uri": "BlockXB_L22_201.b3dm"}, "geometricError": 0.635205864906311}], "content": {"uri": "BlockXB_L21_72.b3dm"}, "geometricError": 1.07666969299316}], "content": {"uri": "BlockXB_L20_28.b3dm"}, "geometricError": 1.08998548984528}, {"boundingVolume": {"box": [-206.579884834839, 66.7259197024931, -23.8849493005384, 12.4420871734619, 0, 0, 0, 22.1632690429688, 0, 0, 0, 13.3516263961792]}, "children": [{"boundingVolume": {"box": [-208.058584488182, 58.1937192815243, -30.5620044841368, 10.9537224769592, 0, 0, 0, 4.87600827217102, 0, 0, 0, 6.67457175254822]}, "content": {"uri": "BlockXB_L21_71.b3dm"}, "geometricError": 0.684601783752441}, {"boundingVolume": {"box": [-211.757395548158, 53.816200049087, -18.1520490893983, 7.25518202781677, 0, 0, 0, 9.25354909896851, 0, 0, 0, 5.73578023910522]}, "children": [{"boundingVolume": {"box": [-211.758708201369, 48.4183274924468, -18.1169331094631, 7.25426602363586, 0, 0, 0, 3.8556764125824, 0, 0, 0, 5.70167350769043]}, "content": {"uri": "BlockXB_L22_199.b3dm"}, "geometricError": 0.453392028808594}, {"boundingVolume": {"box": [-211.757971749878, 57.6717820202357, -18.3141219394795, 7.25432324409485, 0, 0, 0, 5.39796757698059, 0, 0, 0, 5.57370686531067]}, "content": {"uri": "BlockXB_L22_198.b3dm"}, "geometricError": 0.453395843505859}], "content": {"uri": "BlockXB_L21_70.b3dm"}, "geometricError": 0.578338861465454}, {"boundingVolume": {"box": [-199.322290394594, 53.8163764609366, -17.2106730442531, 5.1835150718689, 0, 0, 0, 9.25356769561768, 0, 0, 0, 6.67735028266907]}, "children": [{"boundingVolume": {"box": [-199.323023462558, 49.9607597566872, -16.8692977154752, 5.18272256851196, 0, 0, 0, 5.39795064926147, 0, 0, 0, 6.33231377601624]}, "content": {"uri": "BlockXB_L22_197.b3dm"}, "geometricError": 0.337364315986633}, {"boundingVolume": {"box": [-199.322629884381, 59.212207740758, -17.9576748832535, 5.18262624740601, 0, 0, 0, 3.85773646831512, 0, 0, 0, 5.93034815788269]}, "content": {"uri": "BlockXB_L22_196.b3dm"}, "geometricError": 0.323911666870117}], "content": {"uri": "BlockXB_L21_69.b3dm"}, "geometricError": 0.578338861465454}, {"boundingVolume": {"box": [-206.579884877827, 75.9746326332686, -24.2901592132811, 12.4420871734619, 0, 0, 0, 12.9145555496216, 0, 0, 0, 12.7993268966675]}, "children": [{"boundingVolume": {"box": [-206.578557773131, 70.6265296040735, -30.8400096393466, 12.4385323524475, 0, 0, 0, 7.56158518791199, 0, 0, 0, 6.24775242805481]}, "content": {"uri": "BlockXB_L22_195.b3dm"}, "geometricError": 0.777399063110352}, {"boundingVolume": {"box": [-206.57711606709, 70.6264916017001, -18.3893422837876, 12.4371433258057, 0, 0, 0, 7.56092548370361, 0, 0, 0, 6.20318961143494]}, "content": {"uri": "BlockXB_L22_194.b3dm"}, "geometricError": 0.777316093444824}, {"boundingVolume": {"box": [-206.579853371915, 83.5324954050887, -30.5848942334691, 12.4373888969421, 0, 0, 0, 5.34928321838379, 0, 0, 0, 6.35830783843994]}, "content": {"uri": "BlockXB_L22_193.b3dm"}, "geometricError": 0.777335166931152}, {"boundingVolume": {"box": [-211.759573778307, 81.5656087146518, -17.8593005234682, 7.254807472229, 0, 0, 0, 3.38221597671509, 0, 0, 0, 6.36756944656372]}, "content": {"uri": "BlockXB_L22_192.b3dm"}, "geometricError": 0.453429222106934}, {"boundingVolume": {"box": [-199.324553265112, 85.1901314058418, -18.8523001414684, 5.18221402168274, 0, 0, 0, 3.69536459445953, 0, 0, 0, 5.37484121322632]}, "content": {"uri": "BlockXB_L22_191.b3dm"}, "geometricError": 0.323885917663574}], "content": {"uri": "BlockXB_L21_68.b3dm"}, "geometricError": 0.807153224945068}], "content": {"uri": "BlockXB_L20_27.b3dm"}, "geometricError": 1.38563817739487}], "content": {"uri": "BlockXB_L19_13.b3dm"}, "geometricError": 1.86528587341309}, {"boundingVolume": {"box": [-172.824885898709, 74.8392562696369, -26.1246115027863, 21.3178005218506, 0, 0, 0, 30.2763185501099, 0, 0, 0, 13.1625165939331]}, "children": [{"boundingVolume": {"box": [-172.828693341696, 57.1857551050297, -22.4453778836207, 21.3210582733154, 0, 0, 0, 12.6228156089783, 0, 0, 0, 9.45593118667603]}, "children": [{"boundingVolume": {"box": [-185.260730189293, 50.8743130039745, -18.6729940494729, 8.88322305679321, 0, 0, 0, 6.31137347221375, 0, 0, 0, 5.68596363067627]}, "children": [{"boundingVolume": {"box": [-190.441041177674, 50.8750783950515, -18.0689944982522, 3.70230221748352, 0, 0, 0, 6.3121383190155, 0, 0, 0, 5.07748508453369]}, "content": {"uri": "BlockXB_L22_190.b3dm"}, "geometricError": 0.394505023956299}, {"boundingVolume": {"box": [-181.56167351468, 50.8743560278416, -20.9164521412332, 5.18407535552979, 0, 0, 0, 6.31133055686951, 0, 0, 0, 3.43279802799225]}, "content": {"uri": "BlockXB_L22_189.b3dm"}, "geometricError": 0.39445436000824}], "content": {"uri": "BlockXB_L21_67.b3dm"}, "geometricError": 0.555201530456543}, {"boundingVolume": {"box": [-185.260775697912, 63.4993316792036, -22.4726605818017, 8.88326930999756, 0, 0, 0, 6.31385207176208, 0, 0, 0, 9.49548292160034]}, "children": [{"boundingVolume": {"box": [-189.699958277689, 63.4981073513475, -25.9935737482958, 4.44338512420654, 0, 0, 0, 6.31262755393982, 0, 0, 0, 5.11000823974609]}, "content": {"uri": "BlockXB_L22_188.b3dm"}, "geometricError": 0.394535064697266}, {"boundingVolume": {"box": [-180.820858313431, 63.4969014228552, -26.4324762293931, 4.44329071044922, 0, 0, 0, 6.31130957603455, 0, 0, 0, 5.54874491691589]}, "content": {"uri": "BlockXB_L22_187.b3dm"}, "geometricError": 0.39445436000824}, {"boundingVolume": {"box": [-185.256564956985, 63.4980484933995, -16.9310846132183, 8.88552570343018, 0, 0, 0, 6.3124725818634, 0, 0, 0, 3.95280826091766]}, "content": {"uri": "BlockXB_L22_186.b3dm"}, "geometricError": 0.555344581604004}], "content": {"uri": "BlockXB_L21_66.b3dm"}, "geometricError": 0.555204391479492}, {"boundingVolume": {"box": [-163.944736226108, 57.1858575529203, -18.6587391317465, 12.4380259513855, 0, 0, 0, 12.6227121353149, 0, 0, 0, 4.96592378616333]}, "children": [{"boundingVolume": {"box": [-163.94314018537, 52.9783261013765, -20.3203915784242, 12.4376993179321, 0, 0, 0, 8.41518068313599, 0, 0, 0, 2.55030834674835]}, "content": {"uri": "BlockXB_L22_185.b3dm"}, "geometricError": 0.777356147766113}, {"boundingVolume": {"box": [-173.270439991517, 65.6018566369579, -18.6544298601857, 3.11039924621582, 0, 0, 0, 4.20864319801331, 0, 0, 0, 4.96346735954285]}, "content": {"uri": "BlockXB_L22_184.b3dm"}, "geometricError": 0.26303768157959}, {"boundingVolume": {"box": [-167.053188963061, 65.6008225127834, -18.8049052990755, 3.11068224906921, 0, 0, 0, 4.2075469493866, 0, 0, 0, 4.32789158821106]}, "content": {"uri": "BlockXB_L22_183.b3dm"}, "geometricError": 0.262969493865967}, {"boundingVolume": {"box": [-157.724288630711, 65.6009598838393, -19.0150141684181, 6.21881699562073, 0, 0, 0, 4.20761013031006, 0, 0, 0, 3.86953556537628]}, "content": {"uri": "BlockXB_L22_182.b3dm"}, "geometricError": 0.388675689697266}], "content": {"uri": "BlockXB_L21_65.b3dm"}, "geometricError": 0.788908720016479}], "content": {"uri": "BlockXB_L20_26.b3dm"}, "geometricError": 1.33256149291992}, {"boundingVolume": {"box": [-172.819593285132, 87.4541340955775, -26.429732980692, 21.3230924606323, 0, 0, 0, 17.6461801528931, 0, 0, 0, 12.8676257133484]}, "children": [{"boundingVolume": {"box": [-181.706572056074, 76.9565344777932, -24.8610841939605, 12.4360594749451, 0, 0, 0, 7.1485800743103, 0, 0, 0, 10.918119430542]}, "children": [{"boundingVolume": {"box": [-186.887762820096, 76.9539138715282, -24.9384448135318, 7.25486874580383, 0, 0, 0, 7.15079379081726, 0, 0, 0, 10.8407588005066]}, "content": {"uri": "BlockXB_L22_181.b3dm"}, "geometricError": 0.453418731689453}, {"boundingVolume": {"box": [-174.452877522883, 76.9566420240695, -24.2811772075869, 5.18219304084778, 0, 0, 0, 7.14847254753113, 0, 0, 0, 10.3266844749451]}, "content": {"uri": "BlockXB_L22_180.b3dm"}, "geometricError": 0.446776807308197}], "content": {"uri": "BlockXB_L21_64.b3dm"}, "geometricError": 0.777241706848145}, {"boundingVolume": {"box": [-181.707609426217, 91.2220133607876, -25.8898682177921, 12.435076713562, 0, 0, 0, 7.11734247207642, 0, 0, 0, 11.5225534439087]}, "children": [{"boundingVolume": {"box": [-187.925894093952, 88.85649087521, -25.8974488515007, 6.21851825714111, 0, 0, 0, 4.7518196105957, 0, 0, 0, 11.5149660110474]}, "content": {"uri": "BlockXB_L22_179.b3dm"}, "geometricError": 0.388653755187988}, {"boundingVolume": {"box": [-175.48922688968, 91.2184939028889, -26.031184066536, 6.22022294998169, 0, 0, 0, 7.11858820915222, 0, 0, 0, 11.2672162055969]}, "content": {"uri": "BlockXB_L22_178.b3dm"}, "geometricError": 0.444912195205688}], "content": {"uri": "BlockXB_L21_63.b3dm"}, "geometricError": 0.777185440063477}, {"boundingVolume": {"box": [-160.38796276456, 75.6989874381181, -20.8585442402014, 8.88511371612549, 0, 0, 0, 5.89068269729614, 0, 0, 0, 6.40123248100281]}, "children": [{"boundingVolume": {"box": [-164.092096856499, 72.7559539943177, -21.4779740918542, 5.18436980247498, 0, 0, 0, 2.94764912128448, 0, 0, 0, 5.74250745773315]}, "content": {"uri": "BlockXB_L22_177.b3dm"}, "geometricError": 0.324023246765137}, {"boundingVolume": {"box": [-164.089781915891, 78.6433222743753, -21.4885815071333, 5.18298983573914, 0, 0, 0, 2.94622671604156, 0, 0, 0, 5.76471781730652]}, "content": {"uri": "BlockXB_L22_176.b3dm"}, "geometricError": 0.323933601379395}, {"boundingVolume": {"box": [-155.207252310043, 72.7550212609284, -18.7882086184087, 3.70760154724121, 0, 0, 0, 2.94658744335175, 0, 0, 0, 4.33432245254517]}, "content": {"uri": "BlockXB_L22_175.b3dm"}, "geometricError": 0.231723785400391}, {"boundingVolume": {"box": [-155.210616945928, 78.6434122665875, -20.1664716959115, 3.70384001731873, 0, 0, 0, 2.94850409030914, 0, 0, 0, 5.59507060050964]}, "content": {"uri": "BlockXB_L22_174.b3dm"}, "geometricError": 0.231494903564453}], "content": {"uri": "BlockXB_L21_62.b3dm"}, "geometricError": 0.555319786071777}, {"boundingVolume": {"box": [-160.389302880173, 93.3404917993779, -26.4260181163191, 8.8873085975647, 0, 0, 0, 11.7537140846252, 0, 0, 0, 12.8526635169983]}, "children": [{"boundingVolume": {"box": [-160.390763112702, 93.3392933705827, -31.7905142976771, 8.88406848907471, 0, 0, 0, 11.7510786056519, 0, 0, 0, 7.47802066802979]}, "content": {"uri": "BlockXB_L22_173.b3dm"}, "geometricError": 0.734444826841354}, {"boundingVolume": {"box": [-160.391178633445, 93.3387147722169, -18.9424281600261, 8.88311386108398, 0, 0, 0, 11.7503743171692, 0, 0, 0, 5.37061524391174]}, "content": {"uri": "BlockXB_L22_172.b3dm"}, "geometricError": 0.734396249055862}], "content": {"uri": "BlockXB_L21_61.b3dm"}, "geometricError": 0.734609514474869}], "content": {"uri": "BlockXB_L20_25.b3dm"}, "geometricError": 1.33268547058105}], "content": {"uri": "BlockXB_L19_12.b3dm"}, "geometricError": 1.89225850999355}], "content": {"uri": "BlockXB_L16_2.b3dm"}, "geometricError": 3.25042927265167}}