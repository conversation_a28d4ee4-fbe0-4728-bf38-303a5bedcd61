<!--
 * @Author: Strayer
 * @Date: 2025-11-11
 * @LastEditors: Strayer
 * @LastEditTime: 2025-11-11
 * @Description: 根据地图等级控制渲染的3D流动管道组件
 * @FilePath: \demo2\src\components\LevelBasedPipe.vue
-->
<template>
  <div class="level-based-pipe-container">
    <div id="cesiumContainer" class="cesium-container"></div>
    <div class="info-panel">
      <h3>地图等级控制渲染</h3>
      <div class="info-item">
        <label>当前缩放等级:</label>
        <span class="zoom-level">{{ currentZoomLevel.toFixed(1) }}</span>
      </div>
      <div class="info-item">
        <label>当前渲染模式:</label>
        <span class="render-mode">{{ currentRenderMode }}</span>
      </div>
      <div class="render-rules">
        <h4>渲染规则:</h4>
        <ul>
          <li>等级 > 15: 显示精细管道</li>
          <li>等级 13-15: 显示中等管道</li>
          <li>等级 < 13: 显示发光折线 + 流光折线</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'cesium/Build/Cesium/Widgets/widgets.css'
import {
  Cartesian3,
  Ion,
  Terrain,
  Viewer,
  Math as CesiumMath,
  Color,
  CornerType,
  Cartesian2,
  JulianDate,
  SampledPositionProperty,
  ClockRange,
  ClockStep,
  PolylineGlowMaterialProperty,
  MaterialProperty,
  Event,
  Material,
} from 'cesium'
import { onMounted, onBeforeUnmount, ref } from 'vue'

// Your access token can be found at: https://ion.cesium.com/tokens.
Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0NzBiNjMyNS04MDM4LTQxMzctOWE2Ny00ODJkNjlmNDc0NmQiLCJpZCI6Mjc3ODcwLCJpYXQiOjE3NDAxMDY1OTJ9.fh6XIQqsIU5bzaQPWKo8glgzCN1CVnjq3UPKpzZRDPg'

let viewer: Viewer
let finePipe: any = null
let mediumPipe: any = null
let glowingLine: any = null
let flowingLine: any = null

// 响应式数据
const currentZoomLevel = ref(0)
const currentRenderMode = ref('初始化中...')

// 管道路径坐标
const pipeCoordinates = [
  [120.727823, 31.266826, 10],
  [120.737823, 31.266826, 15],
  [120.737823, 31.276826, 20],
  [120.727823, 31.276826, 25],
  [120.720823, 31.276826, 30],
  [120.720823, 31.266826, 35],
]

onMounted(() => {
  initCesiumMap()
})

onBeforeUnmount(() => {
  if (viewer) viewer.destroy()
})

/**
 * @description: 初始化Cesium地图
 */
async function initCesiumMap() {
  // 初始化地图
  viewer = new Viewer('cesiumContainer', {
    terrain: Terrain.fromWorldTerrain(),
    shouldAnimate: true,
    timeline: false,
    animation: false,
    fullscreenButton: false,
    vrButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    geocoder: false,
    homeButton: false,
    navigationHelpButton: false,
  })
  
  // 隐藏版权信息
  ;(viewer.cesiumWidget.creditContainer as HTMLDivElement).style.display = 'none'

  // 启用地形深度检测
  viewer.scene.globe.depthTestAgainstTerrain = true
  // 允许相机进入地下
  viewer.scene.screenSpaceCameraController.enableCollisionDetection = false

  // 启用地球半透模式
  viewer.scene.globe.translucency.enabled = true
  viewer.scene.globe.translucency.frontFaceAlpha = 0.9

  viewer.scene.globe.enableLighting = false
  viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.shadows = false
  viewer.scene.debugShowFramesPerSecond = true

  // 1米内的剔除；防止模型闪烁
  viewer.camera.frustum.near = 1

  // 设置相机位置
  viewer.camera.flyTo({
    destination: Cartesian3.fromDegrees(120.732823, 31.271826, 500),
    orientation: {
      heading: CesiumMath.toRadians(0.0),
      pitch: CesiumMath.toRadians(-45.0),
    }
  })

  // 监听相机变化
  viewer.camera.changed.addEventListener(updateRenderingByZoom)
  
  // 初始渲染
  updateRenderingByZoom()
}

/**
 * @description: 定义圆形截面生成函数
 */
function computeCircle(radius: number) {
  const positions = []
  for (let i = 0; i < 360; i++) {
    const radians = CesiumMath.toRadians(i)
    positions.push(
      new Cartesian2(
        radius * Math.cos(radians),
        radius * Math.sin(radians),
      ),
    )
  }
  return positions
}

/**
 * @description: 创建精细管道
 */
function createFinePipe() {
  const positions = []
  for (let i = 0; i < pipeCoordinates.length; i++) {
    positions.push(pipeCoordinates[i][0], pipeCoordinates[i][1], pipeCoordinates[i][2])
  }

  return viewer.entities.add({
    name: '精细3D管道',
    polylineVolume: {
      positions: Cartesian3.fromDegreesArrayHeights(positions),
      shape: computeCircle(2.0),
      material: Color.fromCssColorString('#00BFFF').withAlpha(0.8),
      cornerType: CornerType.ROUNDED,
      granularity: CesiumMath.RADIANS_PER_DEGREE,
    },
  })
}

/**
 * @description: 创建中等管道
 */
function createMediumPipe() {
  const positions = []
  for (let i = 0; i < pipeCoordinates.length; i++) {
    positions.push(pipeCoordinates[i][0], pipeCoordinates[i][1], pipeCoordinates[i][2])
  }

  return viewer.entities.add({
    name: '中等3D管道',
    polylineVolume: {
      positions: Cartesian3.fromDegreesArrayHeights(positions),
      shape: computeCircle(5.0),
      material: Color.fromCssColorString('#00FF00').withAlpha(0.6),
      cornerType: CornerType.ROUNDED,
      granularity: CesiumMath.RADIANS_PER_DEGREE,
    },
  })
}

/**
 * @description: 创建发光折线
 */
function createGlowingLine() {
  const positions = []
  for (let i = 0; i < pipeCoordinates.length; i++) {
    positions.push(Cartesian3.fromDegrees(
      pipeCoordinates[i][0],
      pipeCoordinates[i][1],
      pipeCoordinates[i][2] + 2
    ))
  }

  return viewer.entities.add({
    name: '发光折线',
    polyline: {
      positions: positions,
      width: 20,
      material: new PolylineGlowMaterialProperty({
        color: Color.fromCssColorString('#FFFF00'),
        glowPower: 0.1
      })
    }
  })
}

/**
 * @description: 创建流光折线
 */
function createFlowingLine() {
  // 创建简化的流光材质
  class SimpleFlowMaterialProperty implements MaterialProperty {
    private _definitionChanged = new Event()
    private _color: Color
    private _time: number
    private _startTime: JulianDate

    constructor(color: Color) {
      this._color = color
      this._time = 0.0
      this._startTime = JulianDate.now()
    }

    getType(): string {
      return 'SimpleFlow'
    }

    getValue(time: JulianDate, result?: any): any {
      if (!result) {
        result = {}
      }
      
      const currentTime = JulianDate.secondsDifference(time, this._startTime)
      this._time = currentTime

      result.color = this._color
      result.time = this._time
      return result
    }

    equals(other?: MaterialProperty): boolean {
      return other instanceof SimpleFlowMaterialProperty && this._color.equals(other._color)
    }

    get definitionChanged() {
      return this._definitionChanged
    }

    get isConstant() {
      return false
    }
  }

  // 注册简化材质
  if (!(Material as any).SimpleFlowType) {
    (Material as any).SimpleFlowType = 'SimpleFlow';
    (Material as any)._materialCache.addMaterial((Material as any).SimpleFlowType, {
      fabric: {
        type: (Material as any).SimpleFlowType,
        uniforms: {
          color: Color.CYAN,
          time: 0.0
        },
        source: `
          czm_material czm_getMaterial(czm_materialInput materialInput) {
            czm_material material = czm_getDefaultMaterial(materialInput);
            float glow = sin(time * 2.0 + materialInput.st.s * 10.0) * 0.5 + 0.5;
            material.emission = color.rgb * glow;
            material.alpha = color.a * glow;
            return material;
          }
        `
      },
      translucent: function () {
        return true;
      }
    });
  }

  const positions = []
  for (let i = 0; i < pipeCoordinates.length; i++) {
    positions.push(Cartesian3.fromDegrees(
      pipeCoordinates[i][0],
      pipeCoordinates[i][1],
      pipeCoordinates[i][2] + 4
    ))
  }

  return viewer.entities.add({
    name: '流光折线',
    polyline: {
      positions: positions,
      width: 15,
      material: new SimpleFlowMaterialProperty(Color.CYAN)
    }
  })
}

/**
 * @description: 根据缩放等级更新渲染
 */
function updateRenderingByZoom() {
  const height = viewer.camera.positionCartographic.height
  const zoomLevel = Math.log2(40075016.686 / height)
  currentZoomLevel.value = zoomLevel

  if (zoomLevel > 15) {
    // 显示精细管道，隐藏其他
    currentRenderMode.value = '精细管道模式'
    
    if (!finePipe) {
      finePipe = createFinePipe()
    }
    if (mediumPipe) {
      viewer.entities.remove(mediumPipe)
      mediumPipe = null
    }
    if (glowingLine) {
      viewer.entities.remove(glowingLine)
      glowingLine = null
    }
    if (flowingLine) {
      viewer.entities.remove(flowingLine)
      flowingLine = null
    }
  } else if (zoomLevel > 13) {
    // 显示中等管道，隐藏其他
    currentRenderMode.value = '中等管道模式'
    
    if (finePipe) {
      viewer.entities.remove(finePipe)
      finePipe = null
    }
    if (!mediumPipe) {
      mediumPipe = createMediumPipe()
    }
    if (glowingLine) {
      viewer.entities.remove(glowingLine)
      glowingLine = null
    }
    if (flowingLine) {
      viewer.entities.remove(flowingLine)
      flowingLine = null
    }
  } else {
    // 显示发光折线和流光折线，隐藏管道
    currentRenderMode.value = '发光折线 + 流光折线模式'
    
    if (finePipe) {
      viewer.entities.remove(finePipe)
      finePipe = null
    }
    if (mediumPipe) {
      viewer.entities.remove(mediumPipe)
      mediumPipe = null
    }
    if (!glowingLine) {
      glowingLine = createGlowingLine()
    }
    if (!flowingLine) {
      flowingLine = createFlowingLine()
    }
  }
}
</script>

<style scoped>
.level-based-pipe-container {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 280px;
  font-family: Arial, sans-serif;
}

.info-panel h3 {
  margin: 0 0 15px 0;
  color: #00ffff;
  text-align: center;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item label {
  font-size: 14px;
  color: #ccc;
}

.zoom-level {
  font-size: 16px;
  font-weight: bold;
  color: #00ff00;
}

.render-mode {
  font-size: 14px;
  font-weight: bold;
  color: #ffff00;
}

.render-rules {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.render-rules h4 {
  margin: 0 0 10px 0;
  color: #00ffff;
  font-size: 14px;
}

.render-rules ul {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
  line-height: 1.5;
}

.render-rules li {
  margin-bottom: 5px;
  color: #ccc;
}
</style>
